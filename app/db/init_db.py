from app.db.postgres import engine
from app.db.neo4j import create_constraints
from app.modules.organisation.models.invites import Base

def init_db():
    """
    Initialize the database by creating all tables.
    """
    # Import all models to ensure they are registered with Base
    # creates all table
    Base.metadata.create_all(bind=engine)
    print("Database initialized successfully.")

def init_neo4j():
    """
    Initialize the Neo4j database with constraints.
    """
    create_constraints()

def init_all():
    """
    Initialize all databases (PostgreSQL and Neo4j).
    """
    # Initialize PostgreSQL
    init_db()
    
    # Initialize Neo4j
    init_neo4j()
    
    #print("All databases initialized successfully.")


if __name__ == "__main__":
    init_all()