import logging
from typing import Optional
import redis

from app.core.config import settings
from app.utils.redis.redis_client import RedisClient



logger = logging.getLogger(__name__)



class RedisService:
    def __init__(self):
        self.redis_client = RedisClient(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.REDIS_DB,
            password=settings.REDIS_PASSWORD,
        )
        self.r_client = None
        self.expiration_time = int(settings.REDIS_JWT_ACCESS_EXPIRE_SEC)
        self.connect_redis()

    def connect_redis(self) -> None:
        """
        Connects to Redis synchronously.
        """
        try:
            self.r_client = self.redis_client.connect()
            # Verify connection
            self.r_client.ping()
        except Exception as error:
            logger.error("Error connecting to Redis.", exc_info=True)
            raise error

    def close_connection(self) -> None:
        """
        Closes the Redis connection.
        """
        try:
            self.redis_client.close()
            self.r_client = None
        except Exception as error:
            logger.error("Error closing Redis connection.", exc_info=True)
            raise error

    def store_token(self, user_id: str, token: str, ttl: int = None) -> None:
        """
        Stores a JWT token in Redis for a specific user.
        """
        try:
            redis_key = f"user:{user_id}"
            token_key = f"token:{token}"
            ex_time = int(self.expiration_time) if ttl is None else int(ttl)
            self.r_client.set(token_key, redis_key, ex=ex_time)
        except Exception as error:
            logger.error(f"Error storing token for user {user_id}", exc_info=True)
            raise error

    def set_data_in_redis_with_ttl(self, hash_key: str, ttl: int, data: str) -> None:
        """
        Stores data in Redis with a time-to-live.
        """
        try:
            self.r_client.set(hash_key, data)
            self.r_client.expire(hash_key, ttl)
        except Exception as error:
            logger.error(f"Error setting data with TTL for key {hash_key}", exc_info=True)
            raise error

    def set_data_in_redis(self, hash_key: str, sub_key: str, data: str) -> None:
        """
        Stores data in a Redis hash.
        """
        try:
            self.r_client.hset(hash_key, sub_key, data)
        except Exception as error:
            logger.error(f"Error setting hash data for key {hash_key}", exc_info=True)
            raise error

    def set_ttl_hset(self, hash_key: str, ttl: int) -> None:
        """
        Sets the TTL for a Redis key.
        """
        try:
            self.r_client.expire(hash_key, ttl)
        except Exception as error:
            logger.error(f"Error setting TTL for key {hash_key}", exc_info=True)
            raise error

    def set_data_in_redis_nx(self, hash_key: str, sub_key: str, data: str) -> None:
        """
        Stores data in a Redis hash only if the field does not exist.
        """
        try:
            self.r_client.hsetnx(hash_key, sub_key, data)
        except Exception as error:
            logger.error(f"Error setting NX data for key {hash_key}", exc_info=True)
            raise error

    def get_data_from_redis(self, hash_key: str, sub_key: str) -> Optional[str]:
        """
        Retrieves a specific field from a Redis hash.
        """
        try:
            return self.r_client.hget(hash_key, sub_key)
        except Exception as error:
            logger.error(f"Error getting hash data for key {hash_key}", exc_info=True)
            raise error

    def get_data_from_redis_using_key(self, hash_key: str) -> Optional[str]:
        """
        Retrieves data directly stored with a key in Redis.
        """
        try:
            return self.r_client.get(hash_key)
        except Exception as error:
            logger.error(f"Error getting data for key {hash_key}", exc_info=True)
            raise error

    def get_all_data_from_redis_by_hash_key(self, hash_key: str) -> dict:
        """
        Retrieves all the fields and values of a Redis hash.
        """
        try:
            return self.r_client.hgetall(hash_key)
        except Exception as error:
            logger.error(f"Error getting all hash data for key {hash_key}", exc_info=True)
            raise error

    def delete_hash_data_from_redis(self, hash_key: str, sub_key: str) -> None:
        """
        Deletes a specific field from a Redis hash.
        """
        try:
            self.r_client.hdel(hash_key, sub_key)
        except Exception as error:
            logger.error(f"Error deleting hash data for key {hash_key}", exc_info=True)
            raise error

    def set_flag_for_user(self, user_id: str, flag_value: bool, expiration_time: int) -> None:
        """
        Sets a boolean flag for a user in Redis with an expiration time.
        """
        try:
            self.r_client.set(f"user:{user_id}:flag", str(flag_value).lower(), ex=expiration_time)
        except Exception as error:
            logger.error(f"Error setting flag for user {user_id}", exc_info=True)
            raise error

    def get_flag_for_user(self, user_id: str) -> bool:
        """
        Retrieves the boolean flag for a specific user from Redis.
        """
        try:
            flag_value = self.r_client.get(f"user:{user_id}:flag")
            return flag_value == "true" if flag_value is not None else False
        except Exception as error:
            logger.error(f"Error getting flag for user {user_id}", exc_info=True)
            raise error

    def delete_flag_for_user(self, user_id: str) -> None:
        """
        Deletes the boolean flag for a specific user from Redis.
        """
        try:
            self.r_client.delete(f"user:{user_id}:flag")
        except Exception as error:
            logger.error(f"Error deleting flag for user {user_id}", exc_info=True)
            raise error

    def delete_data_with_key(self, key: str) -> None:
        """
        Deletes data from Redis using the provided key.
        """
        try:
            self.r_client.delete(key)
        except Exception as error:
            logger.error(f"Error deleting data for key {key}", exc_info=True)
            raise error
    
    def set(self, key: str, value: str, ex: int = None) -> None:
        """
        Set a key-value pair in Redis with optional expiration.
        
        Args:
            key: The key to set
            value: The value to set
            ex: Expiration time in seconds (optional)
        """
        try:
            self.r_client.set(key, value, ex=ex)
        except Exception as error:
            logger.error(f"Error setting value for key {key}", exc_info=True)
            raise error
    
    def get(self, key: str) -> Optional[str]:
        """
        Get a value from Redis by key.
        
        Args:
            key: The key to get
            
        Returns:
            The value or None if not found
        """
        try:
            return self.r_client.get(key)
        except Exception as error:
            logger.error(f"Error getting value for key {key}", exc_info=True)
            raise error
    
    def delete(self, key: str) -> None:
        """
        Delete a key from Redis.
        
        Args:
            key: The key to delete
        """
        try:
            self.r_client.delete(key)
        except Exception as error:
            logger.error(f"Error deleting key {key}", exc_info=True)
            raise error
    
    def keys(self, pattern: str) -> list:
        """
        Find keys matching a pattern.
        
        Args:
            pattern: Pattern to match
            
        Returns:
            List of matching keys
        """
        try:
            return self.r_client.keys(pattern)
        except Exception as error:
            logger.error(f"Error finding keys with pattern {pattern}", exc_info=True)
            raise error
    
    # Sorted Set Methods
    def zadd(self, name: str, mapping: dict) -> None:
        """
        Add to a sorted set.
        
        Args:
            name: Name of the sorted set
            mapping: Dictionary of {value: score}
        """
        try:
            self.r_client.zadd(name, mapping)
        except Exception as error:
            logger.error(f"Error adding to sorted set {name}", exc_info=True)
            raise error
    
    def zrem(self, name: str, *values) -> None:
        """
        Remove from a sorted set.
        
        Args:
            name: Name of the sorted set
            values: Values to remove
        """
        try:
            self.r_client.zrem(name, *values)
        except Exception as error:
            logger.error(f"Error removing from sorted set {name}", exc_info=True)
            raise error
    
    def zrangebyscore(self, name: str, min_score: str, max_score: str, start: int = None, num: int = None) -> list:
        """
        Return a range of values from a sorted set by score.
        
        Args:
            name: Name of the sorted set
            min_score: Minimum score
            max_score: Maximum score
            start: Start offset (optional)
            num: Number of elements (optional)
            
        Returns:
            List of values in the specified score range
        """
        try:
            return self.r_client.zrangebyscore(name, min_score, max_score, start=start, num=num)
        except Exception as error:
            logger.error(f"Error getting range from sorted set {name}", exc_info=True)
            raise error

    def zrange(self, name: str, start: int, end: int) -> list:
        """
        Return a range of values from a sorted set by index.
        
        Args:
            name: Name of the sorted set
            start: Start index
            end: End index
            
        Returns:
            List of values in the specified index range
        """
        try:
            return self.r_client.zrange(name, start, end)
        except Exception as error:
            logger.error(f"Error getting range from sorted set {name}", exc_info=True)
            raise error

    def __del__(self):
        """
        Destructor to ensure connection is closed when object is deleted.
        """
        self.close_connection()
