"""
Google OAuth Token Management System

This module handles OAuth token lifecycle management for Google Drive integration,
including token generation, refresh, validation, and storage.
"""

import json
import secrets
import structlog
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
from urllib.parse import urlencode

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from app.core.config import settings
from app.db.neo4j import execute_read_query, execute_write_query

logger = structlog.get_logger()


class GoogleOAuthManager:
    """Manager for Google OAuth operations and token lifecycle."""
    
    def __init__(self):
        """Initialize OAuth manager with configuration."""
        self.client_id = getattr(settings, 'GOOGLE_CLIENT_ID', None)
        self.client_secret = getattr(settings, 'GOOGLE_CLIENT_SECRET', None)
        self.redirect_uri = getattr(settings, 'GOOGLE_REDIRECT_URI', None)
        
        # OAuth scopes for Google Drive access
        # Note: 'openid' is automatically added by Google when requesting userinfo scopes
        self.scopes = [
            'openid',  # Added to prevent scope mismatch errors
            'https://www.googleapis.com/auth/drive.readonly',
            'https://www.googleapis.com/auth/drive.metadata.readonly',
            'https://www.googleapis.com/auth/userinfo.email',
            'https://www.googleapis.com/auth/userinfo.profile'
        ]
        
        # Validate configuration
        if not all([self.client_id, self.client_secret, self.redirect_uri]):
            logger.warning("Google OAuth configuration incomplete. OAuth functionality will be disabled.")
    
    def is_configured(self) -> bool:
        """Check if OAuth is properly configured."""
        return all([self.client_id, self.client_secret, self.redirect_uri])
    
    def generate_oauth_url(self, organisation_id: str) -> Tuple[bool, str, Optional[str]]:
        """
        Generate OAuth authorization URL for the given organization.
        
        Args:
            organisation_id: Organization ID to associate with OAuth flow
            
        Returns:
            Tuple of (success, oauth_url_or_error_message, state_parameter)
        """
        try:
            if not self.is_configured():
                return False, "OAuth not configured. Missing client credentials.", None
            
            # Generate secure state parameter
            state = self._generate_state_parameter(organisation_id)
            
            # Store state in database with expiry
            success = self._store_oauth_state(organisation_id, state)
            if not success:
                return False, "Failed to store OAuth state", None
            
            # Create OAuth flow
            flow = Flow.from_client_config(
                {
                    "web": {
                        "client_id": self.client_id,
                        "client_secret": self.client_secret,
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "redirect_uris": [self.redirect_uri]
                    }
                },
                scopes=self.scopes
            )
            flow.redirect_uri = self.redirect_uri
            
            # Generate authorization URL
            auth_url, _ = flow.authorization_url(
                access_type='offline',  # Request refresh token
                include_granted_scopes='true',
                state=state,
                prompt='consent'  # Force consent screen to ensure refresh token
            )
            
            logger.info("Generated OAuth URL", organisation_id=organisation_id, state=state[:10] + "...")
            return True, auth_url, state
            
        except Exception as e:
            logger.error("Failed to generate OAuth URL", error=str(e), organisation_id=organisation_id)
            return False, f"Failed to generate OAuth URL: {str(e)}", None
    
    def handle_oauth_callback(self, code: str, state: str) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Handle OAuth callback and exchange code for tokens.
        
        Args:
            code: Authorization code from Google
            state: State parameter for CSRF protection
            
        Returns:
            Tuple of (success, message, token_data)
        """
        try:
            # Validate and consume state parameter
            organisation_id = self._validate_and_consume_state(state)
            if not organisation_id:
                return False, "Invalid or expired OAuth state", None
            
            # Create OAuth flow
            flow = Flow.from_client_config(
                {
                    "web": {
                        "client_id": self.client_id,
                        "client_secret": self.client_secret,
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "redirect_uris": [self.redirect_uri]
                    }
                },
                scopes=self.scopes,
                state=state
            )
            flow.redirect_uri = self.redirect_uri
            
            # Exchange code for tokens
            flow.fetch_token(code=code)
            credentials = flow.credentials
            
            # Get user info
            user_info = self._get_user_info(credentials)
            
            # Prepare token data
            token_data = {
                'access_token': credentials.token,
                'refresh_token': credentials.refresh_token,
                'token_expires_at': credentials.expiry.isoformat() if credentials.expiry else None,
                'scopes': credentials.scopes,
                'user_email': user_info.get('email'),
                'user_name': user_info.get('name'),
                'organisation_id': organisation_id
            }
            
            logger.info("OAuth callback successful", 
                       organisation_id=organisation_id, 
                       user_email=user_info.get('email'))
            
            return True, "OAuth authentication successful", token_data
            
        except Exception as e:
            logger.error("OAuth callback failed", error=str(e), state=state[:10] + "..." if state else "None")
            return False, f"OAuth callback failed: {str(e)}", None
    
    def refresh_access_token(self, organisation_id: str) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Refresh access token using stored refresh token.
        
        Args:
            organisation_id: Organization ID
            
        Returns:
            Tuple of (success, message, new_token_data)
        """
        try:
            # Get current OAuth credentials
            oauth_creds = self._get_oauth_credentials_from_db(organisation_id)
            if not oauth_creds:
                return False, "No OAuth credentials found", None
            
            # Create credentials object
            credentials = Credentials(
                token=oauth_creds.get('access_token'),
                refresh_token=oauth_creds.get('refresh_token'),
                token_uri="https://oauth2.googleapis.com/token",
                client_id=self.client_id,
                client_secret=self.client_secret,
                scopes=oauth_creds.get('scopes', self.scopes)
            )
            
            # Refresh token
            request = Request()
            credentials.refresh(request)
            
            # Prepare new token data
            new_token_data = {
                'access_token': credentials.token,
                'refresh_token': credentials.refresh_token or oauth_creds.get('refresh_token'),
                'token_expires_at': credentials.expiry.isoformat() if credentials.expiry else None,
                'scopes': credentials.scopes,
                'user_email': oauth_creds.get('user_email'),
                'user_name': oauth_creds.get('user_name')
            }
            
            logger.info("Token refresh successful", organisation_id=organisation_id)
            return True, "Token refreshed successfully", new_token_data
            
        except Exception as e:
            logger.error("Token refresh failed", error=str(e), organisation_id=organisation_id)
            return False, f"Token refresh failed: {str(e)}", None
    
    def validate_token(self, organisation_id: str) -> Tuple[bool, str]:
        """
        Validate current access token by making a test API call.
        
        Args:
            organisation_id: Organization ID
            
        Returns:
            Tuple of (is_valid, message)
        """
        try:
            oauth_creds = self._get_oauth_credentials_from_db(organisation_id)
            if not oauth_creds:
                return False, "No OAuth credentials found"
            
            # Create credentials object
            credentials = Credentials(
                token=oauth_creds.get('access_token'),
                refresh_token=oauth_creds.get('refresh_token'),
                token_uri="https://oauth2.googleapis.com/token",
                client_id=self.client_id,
                client_secret=self.client_secret,
                scopes=oauth_creds.get('scopes', self.scopes)
            )
            
            # Test with Drive API
            service = build('drive', 'v3', credentials=credentials)
            about = service.about().get(fields="user").execute()
            
            user_email = about.get('user', {}).get('emailAddress')
            logger.info("Token validation successful", 
                       organisation_id=organisation_id, 
                       user_email=user_email)
            
            return True, f"Token valid for user: {user_email}"
            
        except HttpError as e:
            if e.resp.status == 401:
                return False, "Token expired or invalid"
            else:
                return False, f"API error: {str(e)}"
        except Exception as e:
            logger.error("Token validation failed", error=str(e), organisation_id=organisation_id)
            return False, f"Token validation failed: {str(e)}"
    
    def revoke_token(self, organisation_id: str) -> Tuple[bool, str]:
        """
        Revoke OAuth tokens for the given organization.
        
        Args:
            organisation_id: Organization ID
            
        Returns:
            Tuple of (success, message)
        """
        try:
            oauth_creds = self._get_oauth_credentials_from_db(organisation_id)
            if not oauth_creds:
                return False, "No OAuth credentials found"
            
            # Revoke token with Google
            import requests
            revoke_url = 'https://oauth2.googleapis.com/revoke'
            
            # Try to revoke refresh token first (revokes all tokens)
            token_to_revoke = oauth_creds.get('refresh_token') or oauth_creds.get('access_token')
            
            response = requests.post(revoke_url, 
                                   params={'token': token_to_revoke},
                                   headers={'content-type': 'application/x-www-form-urlencoded'})
            
            if response.status_code == 200:
                logger.info("Token revoked successfully", organisation_id=organisation_id)
                return True, "Token revoked successfully"
            else:
                logger.warning("Token revocation failed", 
                             organisation_id=organisation_id, 
                             status_code=response.status_code)
                return False, f"Token revocation failed: {response.status_code}"
                
        except Exception as e:
            logger.error("Token revocation error", error=str(e), organisation_id=organisation_id)
            return False, f"Token revocation error: {str(e)}"
    
    def get_oauth_credentials(self, organisation_id: str) -> Optional[Credentials]:
        """
        Get Google OAuth credentials object for API calls.
        
        Args:
            organisation_id: Organization ID
            
        Returns:
            Google OAuth credentials object or None
        """
        try:
            oauth_creds = self._get_oauth_credentials_from_db(organisation_id)
            if not oauth_creds:
                return None
            
            credentials = Credentials(
                token=oauth_creds.get('access_token'),
                refresh_token=oauth_creds.get('refresh_token'),
                token_uri="https://oauth2.googleapis.com/token",
                client_id=self.client_id,
                client_secret=self.client_secret,
                scopes=oauth_creds.get('scopes', self.scopes)
            )
            
            # Check if token needs refresh
            if credentials.expired:
                logger.info("Token expired, attempting refresh", organisation_id=organisation_id)
                success, message, new_token_data = self.refresh_access_token(organisation_id)
                if success and new_token_data:
                    # Update credentials with new token
                    credentials = Credentials(
                        token=new_token_data.get('access_token'),
                        refresh_token=new_token_data.get('refresh_token'),
                        token_uri="https://oauth2.googleapis.com/token",
                        client_id=self.client_id,
                        client_secret=self.client_secret,
                        scopes=new_token_data.get('scopes', self.scopes)
                    )
                else:
                    logger.error("Failed to refresh expired token", 
                               organisation_id=organisation_id, 
                               message=message)
                    return None
            
            return credentials
            
        except Exception as e:
            logger.error("Failed to get OAuth credentials", error=str(e), organisation_id=organisation_id)
            return None
    
    def _generate_state_parameter(self, organisation_id: str) -> str:
        """Generate secure state parameter for CSRF protection."""
        # Create state with organisation_id and random component
        random_part = secrets.token_urlsafe(32)
        timestamp = str(int(datetime.utcnow().timestamp()))
        state_data = f"{organisation_id}:{timestamp}:{random_part}"
        return secrets.token_urlsafe(len(state_data.encode()))[:64]  # Limit length
    
    def _store_oauth_state(self, organisation_id: str, state: str) -> bool:
        """Store OAuth state in database with expiry."""
        try:
            expires_at = (datetime.utcnow() + timedelta(minutes=10)).isoformat()
            
            query = """
            MERGE (os:OAuthState {organisation_id: $org_id})
            SET os.state = $state,
                os.expires_at = $expires_at,
                os.created_at = $created_at
            """
            
            params = {
                "org_id": organisation_id,
                "state": state,
                "expires_at": expires_at,
                "created_at": datetime.utcnow().isoformat()
            }
            
            execute_write_query(query, params)
            return True
            
        except Exception as e:
            logger.error("Failed to store OAuth state", error=str(e))
            return False
    
    def _validate_and_consume_state(self, state: str) -> Optional[str]:
        """Validate state parameter and return organisation_id if valid."""
        try:
            # Find and consume state
            query = """
            MATCH (os:OAuthState {state: $state})
            WHERE datetime(os.expires_at) > datetime()
            RETURN os.organisation_id as org_id
            """
            
            result = execute_read_query(query, {"state": state})
            if not result:
                logger.warning("Invalid or expired OAuth state", state=state[:10] + "...")
                return None
            
            organisation_id = result[0]['org_id']
            
            # Delete consumed state
            delete_query = "MATCH (os:OAuthState {state: $state}) DELETE os"
            execute_write_query(delete_query, {"state": state})
            
            return organisation_id
            
        except Exception as e:
            logger.error("Failed to validate OAuth state", error=str(e))
            return None
    
    def _get_user_info(self, credentials: Credentials) -> Dict[str, Any]:
        """Get user information from Google API."""
        try:
            # Use OAuth2 API to get user info
            service = build('oauth2', 'v2', credentials=credentials)
            user_info = service.userinfo().get().execute()
            return user_info
        except Exception as e:
            logger.warning("Failed to get user info", error=str(e))
            return {}
    
    def _get_oauth_credentials_from_db(self, organisation_id: str) -> Optional[Dict[str, Any]]:
        """Get OAuth credentials from database."""
        try:
            query = """
            MATCH (o:Organisation {id: $org_id})-[:HAS_SOURCE]->(s:Source)
            WHERE s.auth_type = 'oauth' AND s.type = 'google_drive'
            RETURN s.oauth_credentials as oauth_creds
            """
            
            result = execute_read_query(query, {"org_id": organisation_id})
            if not result or not result[0].get('oauth_creds'):
                return None
            
            return json.loads(result[0]['oauth_creds'])
            
        except Exception as e:
            logger.error("Failed to get OAuth credentials from database", error=str(e))
            return None


# Global OAuth manager instance
oauth_manager = GoogleOAuthManager()