from app.core.config import settings
from typing import Dict, List, Any, Optional
import structlog
from pinecone import Pinecone, ServerlessSpec

logger = structlog.get_logger()

class PineconeClient:
    """
    Client for interacting with Pinecone vector database.
    """
    
    _instance = None
    
    def __new__(cls, dimension: Optional[int] = None):
        if cls._instance is None:
            cls._instance = super(PineconeClient, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self, dimension: Optional[int] = None):
        if self._initialized:
            return
            
        self.api_key = settings.PINECONE_API_KEY
        self.index_name = settings.PINECONE_INDEX_NAME
        
        # Get dimension from Sentence Transformers if not provided
        if dimension is None:
            try:
                from app.utils.sentence_transformers import get_sentence_transformer_service
                sentence_service = get_sentence_transformer_service()
                dimension = sentence_service.get_embedding_dimension()
                logger.info(f"Using Sentence Transformers dimension: {dimension}")
            except Exception as e:
                logger.warning(f"Could not get dimension from Sentence Transformers: {e}")
                dimension = 768  # Default to Sentence Transformers standard dimension
        
        self.dimension = dimension
        
        if not self.api_key:
            logger.warning("PINECONE_API_KEY not set. Pinecone client will not be initialized.")
            self.client = None
            self.index = None
        else:
            try:
                self.client = Pinecone(api_key=self.api_key)
                
                # Check if index exists, create if it doesn't
                existing_indexes = [index.name for index in self.client.list_indexes()]
                
                if self.index_name not in existing_indexes:
                    logger.info(f"Creating Pinecone index: {self.index_name} with {dimension} dimensions")
                    # Create a serverless index with dynamic dimensions
                    self.client.create_index(
                        name=self.index_name,
                        dimension=dimension,  # Dynamic dimension based on embedding service
                        metric="cosine",
                        spec=ServerlessSpec(
                            cloud="aws",
                            region="us-east-1"
                        )
                    )
                
                self.index = self.client.Index(self.index_name)
                logger.info(f"Pinecone client initialized with index: {self.index_name}")
                
            except Exception as e:
                logger.error(f"Error initializing Pinecone client: {str(e)}")
                self.client = None
                self.index = None
        
        self._initialized = True
    
    def is_initialized(self) -> bool:
        """
        Check if the Pinecone client is initialized.
        
        Returns:
            Boolean indicating if the client is initialized
        """
        return self.client is not None and self.index is not None
    
    def get_index_dimension(self) -> Optional[int]:
        """
        Get the dimension of the current Pinecone index.
        
        Returns:
            Index dimension or None if not initialized
        """
        if not self.is_initialized():
            return None
        
        try:
            index_stats = self.client.describe_index(self.index_name)
            return index_stats.dimension
        except Exception as e:
            logger.error(f"Error getting index dimension: {e}")
            return self.dimension  # Return configured dimension as fallback
    
    def get_configured_dimension(self) -> Optional[int]:
        """
        Get the configured dimension for this client.
        
        Returns:
            Configured dimension
        """
        return getattr(self, 'dimension', None)