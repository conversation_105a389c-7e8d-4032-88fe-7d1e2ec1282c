"""
Hybrid Search Engine for Enterprise KG

This module provides the main hybrid search engine that combines vector similarity search
results (chunk indices) with graph-based retrieval (GraphRAG) to deliver comprehensive
and contextually rich search results.
"""

import logging
from typing import List, Dict, Any, Optional, Set
from datetime import datetime

from app.utils.pinecone.pinecone_service import PineconeService

from ...db.neo4j import Neo4jSessionManager, get_neo4j_session, execute_read_query, execute_write_query
from .search_schemas import (
    SearchQuery,
    SearchResult,
    SearchStrategy,
    SearchMetrics,
    RoutingDecision,
    VectorMetadata,
    GraphContext
)
from ...constants.entities import get_all_entity_types, get_entity_category_mapping
from ...constants.relationships import get_all_relationship_types, get_relationship_category_mapping
from .graph_rag import GraphRAG
from .search_router import SearchRouter
from .result_aggregator import SearchResultAggregator
from .search_strategies import (
    EntityCentricStrategy,
    RelationshipCentricStrategy,
    ChunkExpansionStrategy,
    HierarchicalStrategy
)

logger = logging.getLogger(__name__)


class HybridSearchEngine:
    """
    Main hybrid search engine that combines vector search with GraphRAG.
    
    This engine takes chunk indices (typically from Pinecone vector similarity search)
    and enriches them with graph-based context using Neo4j knowledge graph traversal.
    All entity and relationship types are dynamically loaded from the constants module.
    """
    
    def __init__(self, pinecone_service=None, enable_intelligent_routing: bool = True, enable_advanced_reranking: bool = True):
        """
        Initialize the hybrid search engine.

        Args:
            pinecone_service: PineconeService instance for vector search
            enable_intelligent_routing: Whether to enable intelligent query routing
            enable_advanced_reranking: Whether to enable advanced reranking techniques
        """
        # Initialize intelligent routing
        self.enable_intelligent_routing = enable_intelligent_routing
        self.enable_advanced_reranking = False
        self.pinecone_service = PineconeService()
        
        # Load available types from constants
        self.available_entity_types = get_all_entity_types()
        self.available_relationship_types = get_all_relationship_types()
        self.entity_categories = get_entity_category_mapping()
        self.relationship_categories = get_relationship_category_mapping()
        
        # Initialize components
        self.graph_rag = GraphRAG()
        self.search_router = SearchRouter() if enable_intelligent_routing else None
        self.result_aggregator = SearchResultAggregator()
        
        # Initialize strategy instances
        self.strategies = {
            'entity_centric': EntityCentricStrategy(self.graph_rag),
            'relationship_centric': RelationshipCentricStrategy(self.graph_rag),
            'chunk_expansion': ChunkExpansionStrategy(self.graph_rag),
            'hierarchical': HierarchicalStrategy(self.graph_rag)
        }
        
        logger.info(f"HybridSearchEngine initialized with {len(self.available_entity_types)} entity types "
                   f"and {len(self.available_relationship_types)} relationship types")
    
    def search(
        self,
        chunk_indices: List[str],
        query_text: Optional[str] = None,
        strategy: SearchStrategy = SearchStrategy.HYBRID,
        max_results: int = 50,
        expansion_depth: int = 2,
        entity_types: Optional[Set[str]] = None,
        relationship_types: Optional[Set[str]] = None,
        entity_categories: Optional[Set[str]] = None,
        min_confidence_score: float = 0.3,
        include_chunk_context: bool = True,
        include_file_context: bool = True,
        boost_recent_entities: bool = True,
        boost_high_importance: bool = True,
        vector_metadata: Optional[List[VectorMetadata]] = None,
        routing_decision: Optional[RoutingDecision] = None
    ) -> SearchResult:
        """
        Execute hybrid search with the given parameters.
        
        Args:
            chunk_indices: List of chunk IDs from vector search
            query_text: Original search query text
            strategy: Search strategy to use
            max_results: Maximum number of results to return
            expansion_depth: Depth for graph traversal
            entity_types: Filter by specific entity types
            relationship_types: Filter by specific relationship types
            entity_categories: Filter by entity categories
            min_confidence_score: Minimum confidence threshold
            include_chunk_context: Whether to include chunk context
            include_file_context: Whether to include file context
            boost_recent_entities: Whether to boost recent entities
            boost_high_importance: Whether to boost high importance entities
            vector_metadata: Metadata from vector search
            routing_decision: Pre-computed routing decision
            
        Returns:
            SearchResult with entities, relationships, and context
        """
        start_time = datetime.now()
        
        try:
            #print(f"Starting hybrid search with {len(chunk_indices)} chunks")
            
            # Create search query object
            search_query = SearchQuery(
                chunk_indices=chunk_indices,
                query_text=query_text,
                strategy=strategy,
                max_results=max_results,
                expansion_depth=expansion_depth,
                entity_types=entity_types or set(),
                relationship_types=relationship_types or set(),
                entity_categories=entity_categories or set(),
                min_confidence_score=min_confidence_score,
                include_chunk_context=include_chunk_context,
                include_file_context=include_file_context,
                boost_recent_entities=boost_recent_entities,
                boost_high_importance=boost_high_importance,
                vector_metadata=vector_metadata or [],
                routing_decision=routing_decision
            )
            
            # Route query to appropriate strategy if routing is enabled
            if self.enable_intelligent_routing and self.search_router and not routing_decision:
                routing_decision = self.search_router.route_query(search_query.query_text, search_query.chunk_indices, search_query.vector_metadata)
                search_query.routing_decision = routing_decision
                #print(f"Query routed to {routing_decision.suggested_strategy} strategy")
            
            # Execute search strategy
            if strategy == SearchStrategy.HYBRID:
                result = self._execute_hybrid_search(search_query)
            else:
                result = self._execute_single_strategy(search_query)
            #print('Hybrid search result ')
            #print('Hybrid search result ', result)
            # Apply advanced reranking if enabled
            if self.enable_advanced_reranking:
                result = self._apply_advanced_reranking(result, search_query)
            
            # Update timing metrics
            total_time = (datetime.now() - start_time).total_seconds() * 1000
            
            #print(f"Hybrid search completed in {total_time:.2f}ms with {len(result.graph_context.entities)} entities")
            return result
            
        except Exception as e:
            logger.error(f"Error in hybrid search: {e}")
            return self._create_error_result(chunk_indices, str(e), routing_decision)
    
    def search_with_query(
        self,
        query_text: str,
        user_id: str,
        top_k: int = 10,
        agent_id: Optional[str] = None,
        organisation_id: Optional[str] = None,
        file_ids: Optional[List[str]] = None,
        **kwargs
    ):
        """
        Search method that accepts query parameters and uses pinecone service for vector search.
        
        This method first performs vector search to get chunk indices, then calls the main
        hybrid search method with those indices.
        
        Args:
            query_text: Search query text
            user_id: User ID for access control
            top_k: Number of results to return
            agent_id: Optional agent ID
            organisation_id: Optional organisation ID
            file_ids: Optional list of file IDs to filter
            
        Returns:
            Search results in the expected format
        """
        try:
            #print('Search with query')
            if not self.pinecone_service:
                logger.error("Pinecone service not available for vector search")
                # Return a mock response that matches expected interface
                class MockResponse:
                    def __init__(self):
                        self.success = False
                        self.message = "Pinecone service not available"
                        self.results = []
                return MockResponse()
            
            # First, get vector search results to obtain chunk indices
            vector_success, vector_message, vector_results = self.pinecone_service.vector_search_only(
                query_text=query_text,
                file_ids=file_ids or [],
                top_k=top_k * 2
            )
            #print(f'Vector search only {vector_success}')
            if not vector_success or not vector_results:
                logger.warning(f"Vector search failed or returned no results: {vector_message}")
                class MockResponse:
                    def __init__(self):
                        self.success = False
                        self.message = vector_message
                        self.results = []
                return MockResponse()
            
            # Extract chunk indices from vector results
            chunk_indices = [result.get('vector_id', '') for result in vector_results if result.get('vector_id')]
            if not chunk_indices:
                logger.warning("No valid chunk indices found in vector search results")
                class MockResponse:
                    def __init__(self):
                        self.success = False
                        self.message = "No valid chunk indices found"
                        self.results = []
                return MockResponse()
            #print(f'Chunk indices found {len(chunk_indices)}')
            
            # Now perform hybrid search with the chunk indices
            hybrid_result = self.search(
                chunk_indices=chunk_indices,
                query_text=query_text,
                max_results=top_k
            )
            
            # Convert SearchResult to expected format
            class HybridResponse:
                def __init__(self, search_result, vector_results, k):
                    self.success = True
                    self.message = f"Found {len(vector_results[:k])} documents using hybrid search"
                    self.results = []
                    
                    # Create chunk-to-entity and chunk-to-relationship mappings
                    self.chunk_entity_map = self._create_chunk_entity_map(search_result)
                    self.chunk_relationship_map = self._create_chunk_relationship_map(search_result)
                    
                    # NEW: Add global graph context to expose ALL graph discoveries
                    self.graph_context = self._create_global_graph_context(search_result)
                    
                    # Convert vector results with chunk-specific enhancements
                    for vector_result in vector_results:
                        enhanced_result = vector_result.copy()
                        enhanced_result['search_type'] = 'hybrid'
                        
                        # Get chunk-specific entities and relationships
                        chunk_id = vector_result.get('vector_id', '')
                        enhanced_result['entities'] = self.chunk_entity_map.get(chunk_id, [])
                        enhanced_result['relationships'] = self.chunk_relationship_map.get(chunk_id, [])
                        
                        self.results.append(enhanced_result)
                    self.results = self.results[:k]  # Limit to top k results
                
                def _create_chunk_entity_map(self, search_result):
                    """Create mapping from chunk IDs to their specific entities."""
                    chunk_entity_map = {}
                    for entity in search_result.graph_context.entities:
                        # Only include entities that have chunk sources for chunk-specific mapping
                        if hasattr(entity, 'chunk_sources') and entity.chunk_sources:
                            for chunk_id in entity.chunk_sources:
                                if chunk_id not in chunk_entity_map:
                                    chunk_entity_map[chunk_id] = []
                                chunk_entity_map[chunk_id].append({
                                    'name': entity.name,
                                    'type': entity.entity_type,
                                    'properties': entity.properties,
                                    'relevance_score': entity.relevance_score,
                                    'match_reason': entity.match_reason
                                })
                    return chunk_entity_map
                
                def _create_chunk_relationship_map(self, search_result):
                    """Create mapping from chunk IDs to their specific relationships."""
                    chunk_relationship_map = {}
                    for relationship in search_result.graph_context.relationships:
                        # Only include relationships that have chunk sources for chunk-specific mapping
                        if hasattr(relationship, 'chunk_sources') and relationship.chunk_sources:
                            for chunk_id in relationship.chunk_sources:
                                if chunk_id not in chunk_relationship_map:
                                    chunk_relationship_map[chunk_id] = []
                                chunk_relationship_map[chunk_id].append({
                                    'source': relationship.source_entity,
                                    'target': relationship.target_entity,
                                    'type': relationship.relationship_type,
                                    'properties': relationship.properties,
                                    'confidence_score': relationship.confidence_score,
                                    'relevance_score': relationship.relevance_score,
                                    'context': relationship.context
                                })
                    return chunk_relationship_map
                
                def _create_global_graph_context(self, search_result):
                    """Create global graph context that includes ALL entities and relationships."""
                    # Serialize all entities
                    all_entities = []
                    chunk_based_entities = []
                    graph_discovered_entities = []
                    
                    for entity in search_result.graph_context.entities:
                        entity_data = {
                            'name': entity.name,
                            'type': entity.entity_type,
                            'properties': entity.properties,
                            'relevance_score': entity.relevance_score,
                            'match_reason': entity.match_reason,
                            'node_id': getattr(entity, 'node_id', None),
                            'created_at': getattr(entity, 'created_at', None),
                            'updated_at': getattr(entity, 'updated_at', None),
                            'chunk_sources': getattr(entity, 'chunk_sources', [])
                        }
                        all_entities.append(entity_data)
                        
                        # Categorize entities
                        if hasattr(entity, 'chunk_sources') and entity.chunk_sources:
                            chunk_based_entities.append(entity_data)
                        else:
                            graph_discovered_entities.append(entity_data)
                    
                    # Serialize all relationships
                    all_relationships = []
                    chunk_based_relationships = []
                    graph_discovered_relationships = []
                    
                    for relationship in search_result.graph_context.relationships:
                        relationship_data = {
                            'source': relationship.source_entity,
                            'target': relationship.target_entity,
                            'type': relationship.relationship_type,
                            'properties': relationship.properties,
                            'confidence_score': relationship.confidence_score,
                            'relevance_score': relationship.relevance_score,
                            'context': relationship.context,
                            'chunk_sources': getattr(relationship, 'chunk_sources', [])
                        }
                        all_relationships.append(relationship_data)
                        
                        # Categorize relationships
                        if hasattr(relationship, 'chunk_sources') and relationship.chunk_sources:
                            chunk_based_relationships.append(relationship_data)
                        else:
                            graph_discovered_relationships.append(relationship_data)
                    
                    return {
                        # All discoveries from GraphRAG
                        'all_entities': all_entities,
                        'all_relationships': all_relationships,
                        
                        # Categorized discoveries
                        'chunk_based_entities': chunk_based_entities,
                        'graph_discovered_entities': graph_discovered_entities,
                        'chunk_based_relationships': chunk_based_relationships,
                        'graph_discovered_relationships': graph_discovered_relationships,
                        
                        # Statistics
                        'total_entities': len(all_entities),
                        'total_relationships': len(all_relationships),
                        'chunk_based_entity_count': len(chunk_based_entities),
                        'graph_discovered_entity_count': len(graph_discovered_entities),
                        'chunk_based_relationship_count': len(chunk_based_relationships),
                        'graph_discovered_relationship_count': len(graph_discovered_relationships),
                        
                        # Graph metadata
                        'entity_types_found': list(search_result.graph_context.entity_types_found) if hasattr(search_result.graph_context, 'entity_types_found') else [],
                        'relationship_types_found': list(search_result.graph_context.relationship_types_found) if hasattr(search_result.graph_context, 'relationship_types_found') else [],
                        'max_depth_reached': getattr(search_result.graph_context, 'max_depth_reached', 0),
                        'expansion_paths': getattr(search_result.graph_context, 'expansion_paths', [])
                    }
            # Create and return the enhanced hybrid response with graph context
            return HybridResponse(hybrid_result, vector_results, top_k)
            
        except Exception as e:
            logger.error(f"Error in search_with_query: {e}")
            class MockResponse:
                def __init__(self):
                    self.success = False
                    self.message = str(e)
                    self.results = []
            return MockResponse()
    
    def _execute_hybrid_search(self, query: SearchQuery) -> SearchResult:
        """Execute hybrid search combining multiple strategies."""
        
        # Determine which strategy to use based on routing decision
        strategy_name = 'entity_centric'  # default

        #print("_execute_hybrid_search", query.routing_decision)

        if query.routing_decision:
            strategy_name = query.routing_decision.suggested_strategy
        
        # Execute primary strategy
        primary_strategy = self.strategies.get(strategy_name, self.strategies['entity_centric'])

        #print("primary_strategy", primary_strategy)

        result = primary_strategy.execute_search(query)
        
        #print("primary_strategy result", result)
        # If we have vector metadata, aggregate with vector results
        if query.vector_metadata:
            vector_results = [
                {
                    'id': vm.chunk_id,
                    'score': vm.similarity_score,
                    'metadata': vm.metadata
                }
                for vm in query.vector_metadata
            ]
            
            #print("aggregating result")        
            result = self.result_aggregator.aggregate_results(
                vector_results=vector_results,
                graph_result=result,
                query=query
            )
            #print("aggregated result")        
        return result
    
    def _execute_single_strategy(self, query: SearchQuery) -> SearchResult:
        """Execute a single search strategy."""
        
        strategy_map = {
            SearchStrategy.ENTITY_CENTRIC: 'entity_centric',
            SearchStrategy.RELATIONSHIP_CENTRIC: 'relationship_centric',
            SearchStrategy.CHUNK_EXPANSION: 'chunk_expansion',
            SearchStrategy.HIERARCHICAL: 'hierarchical'
        }
        
        strategy_name = strategy_map.get(query.strategy, 'entity_centric')
        strategy = self.strategies.get(strategy_name, self.strategies['entity_centric'])
        
        return strategy.execute(query)
    
    def _apply_advanced_reranking(self, result: SearchResult, query: SearchQuery) -> SearchResult:
        """Apply advanced reranking techniques to improve result quality."""
        
        try:
            # Apply business rules
            entities, relationships = self.result_aggregator.apply_business_rules(
                result.entities, result.relationships, query
            )
            
            # Deduplicate results
            entities = self.result_aggregator.deduplicate_entities(entities)
            relationships = self.result_aggregator.deduplicate_relationships(relationships)
            
            # Update result
            result.entities = entities[:query.max_results]
            result.relationships = relationships[:query.max_results]
            
            return result
            
        except Exception as e:
            logger.error(f"Error in advanced reranking: {e}")
            return result
    
    def _create_error_result(
        self,
        chunk_indices: List[str],
        error_message: str,
        routing_decision: Optional[RoutingDecision]
    ) -> SearchResult:
        """Create an error result."""
        
        return SearchResult(
            query="",
            graph_context=GraphContext()
        )
    
    def get_available_entity_types(self) -> Set[str]:
        """Get all available entity types."""
        return self.available_entity_types
    
    def get_available_relationship_types(self) -> Set[str]:
        """Get all available relationship types."""
        return self.available_relationship_types
    
    def get_entity_categories(self) -> Dict[str, str]:
        """Get entity category mappings."""
        return self.entity_categories
    
    def get_relationship_categories(self) -> Dict[str, str]:
        """Get relationship category mappings."""
        return self.relationship_categories
    
    def validate_entity_types(self, entity_types: Set[str]) -> Set[str]:
        """Validate and filter entity types against available types."""
        if not entity_types:
            return set()
        
        valid_types = entity_types.intersection(self.available_entity_types)
        invalid_types = entity_types - valid_types
        
        if invalid_types:
            logger.warning(f"Invalid entity types filtered out: {invalid_types}")
        
        return valid_types
    
    def validate_relationship_types(self, relationship_types: Set[str]) -> Set[str]:
        """Validate and filter relationship types against available types."""
        if not relationship_types:
            return set()
        
        valid_types = relationship_types.intersection(self.available_relationship_types)
        invalid_types = relationship_types - valid_types
        
        if invalid_types:
            logger.warning(f"Invalid relationship types filtered out: {invalid_types}")
        
        return valid_types
    
    def get_search_statistics(self) -> Dict[str, Any]:
        """Get search engine statistics."""
        
        stats = {
            'available_entity_types': len(self.available_entity_types),
            'available_relationship_types': len(self.available_relationship_types),
            'intelligent_routing_enabled': self.enable_intelligent_routing,
            'advanced_reranking_enabled': self.enable_advanced_reranking,
            'strategies_available': list(self.strategies.keys())
        }
        
        # Add routing statistics if available
        if self.search_router:
            stats['routing_statistics'] = self.search_router.get_routing_statistics()
        
        return stats
    
    def update_configuration(self, config: Dict[str, Any]) -> None:
        """Update search engine configuration."""
        
        # Update router configuration
        if self.search_router and 'routing' in config:
            self.search_router.update_routing_config(config['routing'])
        
        # Update aggregator configuration
        if 'aggregation' in config:
            self.result_aggregator = SearchResultAggregator(config['aggregation'])
        
        # Update feature flags
        if 'enable_intelligent_routing' in config:
            self.enable_intelligent_routing = config['enable_intelligent_routing']
        
        if 'enable_advanced_reranking' in config:
            self.enable_advanced_reranking = config['enable_advanced_reranking']
        
        logger.info("Updated hybrid search engine configuration")
