import grpc
import structlog
from typing import Dict, Any
from googleapiclient.discovery import build

from app.grpc_ import google_drive_pb2, google_drive_pb2_grpc
from app.modules.connectors.google_drive.services.google_drive_service import GoogleDriveService
from app.modules.connectors.google_drive.workers.sync_worker import GoogleDriveSyncWorker
from app.modules.organisation.services.organisation import OrganisationService

logger = structlog.get_logger()

def convert_properties_to_string_map(properties):
    """
    Convert a properties dictionary to a string-only map suitable for protobuf.
    
    Args:
        properties (dict): Dictionary with mixed value types
        
    Returns:
        dict: Dictionary with all values converted to strings
    """
    if not properties or not isinstance(properties, dict):
        return {}
    
    string_properties = {}
    for key, value in properties.items():
        # Convert key to string if needed
        str_key = str(key) if not isinstance(key, str) else key
        
        # Convert value to string based on type
        if isinstance(value, bool):
            str_value = "true" if value else "false"
        elif value is None:
            str_value = ""
        elif isinstance(value, (int, float)):
            str_value = str(value)
        elif isinstance(value, str):
            str_value = value
        else:
            # For complex objects, convert to string representation
            str_value = str(value)
        
        string_properties[str_key] = str_value
    
    return string_properties

class GoogleDriveGrpcService(google_drive_pb2_grpc.GoogleDriveServiceServicer):
    """
    gRPC service for Google Drive integration.
    """
    
    def __init__(self):
        self.drive_service = GoogleDriveService()
        self.sync_worker = GoogleDriveSyncWorker()
        self.organisation_service = OrganisationService()
    
    def syncDrive(self, request, context):
        """
        Schedule Google Drive sync to run in background and return immediately.
        """
        logger.info("Received request to sync Google Drive",
                   organisation_id=request.organisation_id,
                   full_sync=request.full_sync)
        
        try:
            # Validate organisation_id is provided
            if not request.organisation_id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("organisation_id is required")
                return google_drive_pb2.SyncDriveResponse(
                    success=False,
                    message="organisation_id is required",
                    sync_status="failed"
                )
            
            # Schedule sync job in background instead of running synchronously
            job_id = self.drive_service._schedule_sync(
                user_id=request.organisation_id,  # Using organisation_id as user_id for scheduling
                organisation_id=request.organisation_id,
                full_sync=request.full_sync,
                delay_seconds=0  # Start immediately
            )
            
            logger.info(f"Google Drive sync job scheduled with ID: {job_id}")
            
            return google_drive_pb2.SyncDriveResponse(
                success=True,
                message=f"Google Drive sync job scheduled successfully. Job ID: {job_id}",
                files_synced=0,  # Will be updated when job completes
                folders_synced=0,  # Will be updated when job completes
                sync_status="scheduled"
            )
            
        except ValueError as e:
            logger.error("Invalid parameters for Google Drive sync", error=str(e))
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details(str(e))
            return google_drive_pb2.SyncDriveResponse(
                success=False,
                message=str(e),
                sync_status="failed"
            )
        except Exception as e:
            logger.error("Error scheduling Google Drive sync", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error scheduling Google Drive sync: {str(e)}")
            return google_drive_pb2.SyncDriveResponse(success=False, sync_status="failed")
    def getSyncStatus(self, request, context):
        """
        Get the status of a sync job by job ID.
        """
        logger.info("Received request to get sync status",
                   job_id=request.job_id)
        
        try:
            # Check if job exists in Redis
            job_data_str = self.drive_service.redis_service.get(request.job_id)
            
            if not job_data_str:
                return google_drive_pb2.SyncStatusResponse(
                    success=False,
                    message="Job not found or expired",
                    status="not_found"
                )
            
            import json
            job_data = json.loads(job_data_str)
            
            # Check if job is still in queue (pending)
            queue_score = self.drive_service.redis_service.zscore("gdrive_sync_queue", request.job_id)
            
            if queue_score is not None:
                return google_drive_pb2.SyncStatusResponse(
                    success=True,
                    message="Sync job is pending in queue",
                    status="pending",
                    organisation_id=job_data.get('organisation_id', ''),
                    full_sync=job_data.get('full_sync', False)
                )
            else:
                # Job has been processed (completed or failed)
                # In a production system, you'd want to store completion status
                return google_drive_pb2.SyncStatusResponse(
                    success=True,
                    message="Sync job has been processed",
                    status="completed",
                    organisation_id=job_data.get('organisation_id', ''),
                    full_sync=job_data.get('full_sync', False)
                )
                
        except Exception as e:
            logger.error("Error getting sync status", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error getting sync status: {str(e)}")
            return google_drive_pb2.SyncStatusResponse(
                success=False,
                message=f"Error: {str(e)}",
                status="error"
            )

    def listTopLevelFolders(self, request, context):
        """
        List top-level folders from Google Drive using service account.
        """
        logger.info("Received request to list top-level folders",
                   organisation_id=request.organisation_id)
        
        try:
            success, message, folders = self.drive_service.fetch_top_level_folders(
                request.organisation_id
            )
            
            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return google_drive_pb2.ListTopLevelFoldersResponse(success=False)
            
            # Convert folders to proto models
            folder_models = []
            for folder in folders:
                folder_model = google_drive_pb2.FolderInfo(
                    id=folder['id'],
                    name=folder['name']
                )
                folder_models.append(folder_model)
            
            return google_drive_pb2.ListTopLevelFoldersResponse(
                success=True,
                message=message,
                folders=folder_models
            )
            
        except Exception as e:
            logger.error("Error listing top-level folders", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error listing folders: {str(e)}")
            return google_drive_pb2.ListTopLevelFoldersResponse(success=False)

    def disconnectDrive(self, request, context):
        """
        Disconnect Google Drive for an organization (remove synced data).
        """
        logger.info("Received request to disconnect Google Drive", 
                   organisation_id=request.organisation_id)
        
        try:
            success, message = self.drive_service.disconnect_drive(request.organisation_id)
            
            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return google_drive_pb2.DisconnectDriveResponse(success=False)
            
            return google_drive_pb2.DisconnectDriveResponse(
                success=True,
                message=message
            )
            
        except Exception as e:
            logger.error("Error disconnecting Google Drive", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error disconnecting Google Drive: {str(e)}")
            return google_drive_pb2.DisconnectDriveResponse(success=False)
    
    def listFiles(self, request, context):
        """
        List Google Drive files and folders.
        """
        logger.info("Received request to list Google Drive files", 
                   user_id=request.user_id, folder_id=request.folder_id)
        
        try:
            files, total_count, page, page_size = self.drive_service.list_files(
                request.user_id,
                request.folder_id if request.folder_id else None,
                request.page if request.page > 0 else 1,
                request.page_size if request.page_size > 0 else 50
            )
            
            # Convert to proto models
            file_models = []
            for file_data in files:
                file_model = google_drive_pb2.DriveFileModel(
                    id=file_data['id'],
                    name=file_data['name'],
                    mime_type=file_data.get('mime_type', ''),
                    web_view_link=file_data.get('web_view_link', ''),
                    created_time=file_data['created_time'],
                    modified_time=file_data['modified_time'],
                    size=int(file_data.get('size', 0)),
                    shared_with=file_data.get('shared_with', []),
                    is_folder=file_data['is_folder'],
                    parent_folder_id=file_data.get('parent_folder_id', ''),
                    child_count=file_data.get('child_count', 0)
                )
                file_models.append(file_model)
            
            return google_drive_pb2.ListFilesResponse(
                success=True,
                message="Files retrieved successfully",
                files=file_models,
                total_count=total_count,
                page=page,
                page_size=page_size
            )
            
        except Exception as e:
            logger.error("Error listing Google Drive files", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error listing files: {str(e)}")
            return google_drive_pb2.ListFilesResponse(success=False)
    
    def getFileDetails(self, request, context):
        """
        Get file details.
        """
        logger.info("Received request to get Google Drive file details", 
                   user_id=request.user_id, file_id=request.file_id)
        
        try:
            success, message, file_data = self.drive_service.get_file_details(
                request.user_id,
                request.file_id
            )
            
            if not success or not file_data:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(message)
                return google_drive_pb2.GetFileDetailsResponse(success=False)
            
            # Convert to proto model
            file_model = google_drive_pb2.DriveFileModel(
                id=file_data['id'],
                name=file_data['name'],
                mime_type=file_data.get('mime_type', ''),
                web_view_link=file_data.get('web_view_link', ''),
                created_time=file_data['created_time'],
                modified_time=file_data['modified_time'],
                size=int(file_data.get('size', 0)),
                shared_with=file_data.get('shared_with', []),
                is_folder=file_data['is_folder'],
                child_count=file_data.get('child_count', 0)
            )
            
            return google_drive_pb2.GetFileDetailsResponse(
                success=True,
                message=message,
                file=file_model
            )
            
        except Exception as e:
            logger.error("Error getting Google Drive file details", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error getting file details: {str(e)}")
            return google_drive_pb2.GetFileDetailsResponse(success=False)
    
    def getFolderById(self, request, context):
        """
        Get folder by ID and its contents using service account.
        """
        logger.info("Received request to get Google Drive folder by ID",
                   organisation_id=request.organisation_id, folder_id=request.folder_id)
        
        try:
            # Get folder and contents using service account
            success, message, folder_data = self.drive_service.get_folder_and_contents_by_id_service_account(
                request.organisation_id,
                request.folder_id
            )
            
            if not success or not folder_data:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(message)
                return google_drive_pb2.GetFolderByIdResponse(success=False)
            
            # Convert folder to proto model
            folder_model = google_drive_pb2.DriveFileModel(
                id=folder_data['id'],
                name=folder_data['name'],
                is_folder=True,
                child_count=folder_data['child_count']
            )
            
            # Convert children to proto models
            children_models = []
            for child_data in folder_data.get('children', []):
                child_model = google_drive_pb2.DriveFileModel(
                    id=child_data['id'],
                    name=child_data['name'],
                    mime_type=child_data.get('mime_type', ''),
                    web_view_link=child_data.get('web_view_link', ''),
                    created_time=child_data['created_time'],
                    modified_time=child_data['modified_time'],
                    size=int(child_data.get('size', 0)),
                    shared_with=child_data.get('shared_with', []),
                    is_folder=child_data['is_folder'],
                    parent_folder_id=child_data.get('parent_folder_id', ''),
                    child_count=child_data.get('child_count', 0)
                )
                children_models.append(child_model)
            
            return google_drive_pb2.GetFolderByIdResponse(
                success=True,
                message=message,
                folder=folder_model,
                children=children_models
            )
            
        except Exception as e:
            logger.error("Error getting Google Drive folder by ID", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error getting folder by ID: {str(e)}")
            return google_drive_pb2.GetFolderByIdResponse(success=False)
    
    def syncFolderByIds(self, request, context):
        """
        Sync specific folders by IDs recursively using service account via worker.
        Creates a job for the worker to process asynchronously.
        """
        logger.info("Received request to sync Google Drive folders by IDs",
                   organisation_id=request.organisation_id, folder_ids=request.folder_ids)
        
        try:
            # Validate that we have credentials before scheduling job
            service = self.drive_service._get_drive_service(request.organisation_id)
            if not service:
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details("No Google Drive service account credentials found")
                return google_drive_pb2.SyncFolderByIdsResponse(
                    success=False,
                    message="No Google Drive service account credentials found",
                    sync_status="failed"
                )
            
            # Import worker here to avoid circular imports
            from app.modules.connectors.google_drive.workers.sync_worker import GoogleDriveSyncWorker
            
            # Create worker instance and schedule the job
            worker = GoogleDriveSyncWorker()
            job_id = worker.schedule_folder_sync_job(
                organisation_id=request.organisation_id,
                folder_ids=list(request.folder_ids),
                delay_seconds=0  # Process immediately
            )
            
            logger.info(f"Scheduled folder sync job {job_id} for organization {request.organisation_id}")
            
            # Return immediate response indicating job was scheduled
            return google_drive_pb2.SyncFolderByIdsResponse(
                success=True,
                message=f"Folder sync job scheduled successfully. Job ID: {job_id}",
                files_synced=0,  # Will be updated when job completes
                folders_synced=0,  # Will be updated when job completes
                sync_status="scheduled",
                synced_folders=[]  # Will be populated when job completes
            )
            
        except Exception as e:
            logger.error("Error scheduling Google Drive folder sync job", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error scheduling folder sync job: {str(e)}")
            return google_drive_pb2.SyncFolderByIdsResponse(
                success=False,
                message=f"Error scheduling folder sync job: {str(e)}",
                sync_status="failed"
            )
            return google_drive_pb2.SyncFolderByIdsResponse(
                success=False,
                message=f"Error: {str(e)}",
                sync_status="failed"
            )
    
    def checkFileAccess(self, request, context):
        """
        Check if a user has access to a file or folder.
        This method recursively checks parent folders if direct access is not found.
        """
        logger.info("Received request to check file access",
                   user_id=request.user_id, file_id=request.file_id)
        
        try:
            has_access = self.drive_service.check_file_access(
                request.user_id,
                request.file_id
            )
            
            return google_drive_pb2.CheckFileAccessResponse(
                success=True,
                message="Access check completed successfully",
                has_access=has_access
            )
            
        except Exception as e:
            logger.error("Error checking file access", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error checking file access: {str(e)}")
            return google_drive_pb2.CheckFileAccessResponse(
                success=False,
                message=f"Error: {str(e)}",
                has_access=False
            )
    
    def searchSimilarDocuments(self, request, context):
        """
        Search for documents semantically similar to the query text.
        """
        logger.info("Received request to search similar documents",
                   user_id=request.user_id, query=request.query_text,
                   agent_id=request.agent_id if hasattr(request, 'agent_id') else None,
                   org_id=request.organisation_id if hasattr(request, 'organisation_id') else None)
        
        try:
            # Extract file_ids from request if provided
            file_ids = None
            if hasattr(request, 'file_ids') and request.file_ids:
                file_ids = list(request.file_ids)
                logger.info(f"Filtering search to {len(file_ids)} specific files")
            
            # Extract least_score from request if provided
            least_score = None
            if hasattr(request, 'least_score') and request.least_score > 0:
                least_score = request.least_score
                logger.info(f"Filtering results with minimum score: {least_score}")
            
            success, message, results = self.drive_service.search_similar_documents(
                request.user_id,
                request.query_text,
                request.top_k if request.top_k > 0 else 5,
                request.agent_id if hasattr(request, 'agent_id') and request.agent_id else None,
                request.organisation_id if hasattr(request, 'organisation_id') and request.organisation_id else None,
                file_ids,
                least_score
            )
            #print('search similar documents', success, message, results)
            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return google_drive_pb2.SearchSimilarDocumentsResponse(success=False)
            
            # Convert to proto models (without entities/relationships in individual results)
            result_models = []
            
            # Check if results is a HybridResponse object with graph_context
            graph_context_data = None
            if hasattr(results, 'graph_context'):
                # Results is a HybridResponse object
                graph_context_data = getattr(results, 'graph_context', None)
                actual_results = getattr(results, 'results', [])
            else:
                # Results is a list of result items (legacy format)
                actual_results = results if isinstance(results, list) else []
            
            # Process individual search results (without entities/relationships)
            for result in actual_results:
                result_model = google_drive_pb2.SearchResultItem(
                    file_id=result['file_id'],
                    file_name=result['file_name'],
                    mime_type=result.get('mime_type', ''),
                    web_view_link=result.get('web_view_link', ''),
                    created_time=result['created_time'],
                    modified_time=result['modified_time'],
                    score=float(result['score']),
                    vector_id=result['vector_id'],
                    chunk_text=result.get('chunk_text', ''),
                    search_type=result.get('search_type', 'hybrid')
                )
                result_models.append(result_model)
            
            # Create GraphContext proto from graph_context_data
            graph_context_proto = None
            if graph_context_data:
                # Convert all entities
                all_entities_proto = []
                for entity in graph_context_data.get('all_entities', []):
                    entity_proto = google_drive_pb2.EntityInfo(
                        id=entity.get('node_id', ''),
                        name=entity.get('name', ''),
                        type=entity.get('type', ''),
                        properties=convert_properties_to_string_map(entity.get('properties', {})),
                        relevance_score=float(entity.get('relevance_score', 0.0))
                    )
                    all_entities_proto.append(entity_proto)
                
                # Convert all relationships
                all_relationships_proto = []
                for relationship in graph_context_data.get('all_relationships', []):
                    relationship_proto = google_drive_pb2.RelationshipInfo(
                        id=relationship.get('id', ''),
                        type=relationship.get('type', ''),
                        source_entity_id=relationship.get('source_entity_id', ''),
                        target_entity_id=relationship.get('target_entity_id', ''),
                        source_entity_name=relationship.get('source', ''),
                        target_entity_name=relationship.get('target', ''),
                        properties=convert_properties_to_string_map(relationship.get('properties', {})),
                        confidence_score=float(relationship.get('confidence_score', 0.0)),
                        relevance_score=float(relationship.get('relevance_score', 0.0)),
                        context=relationship.get('context', '')
                    )
                    all_relationships_proto.append(relationship_proto)
                
                # Create the simplified GraphContext proto
                graph_context_proto = google_drive_pb2.GraphContext(
                    all_entities=all_entities_proto,
                    all_relationships=all_relationships_proto
                )
            
            # Create response with separate graph context
            response = google_drive_pb2.SearchSimilarDocumentsResponse(
                success=True,
                message=message,
                results=result_models
            )
            
            # Add graph context if available
            if graph_context_proto:
                response.graph_context.CopyFrom(graph_context_proto)
            
            return response
            
        except Exception as e:
            #print(e)
            logger.error("Error searching similar documents", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error searching similar documents: {str(e)}")
            return google_drive_pb2.SearchSimilarDocumentsResponse(success=False)
            
    def batchSearchSimilarDocuments(self, request, context):
        """
        Batch search for documents semantically similar to multiple query texts.
        """
        logger.info("Received request for batch search of similar documents",
                   user_id=request.user_id, query_count=len(request.query_texts),
                   agent_id=request.agent_id if hasattr(request, 'agent_id') else None,
                   org_id=request.organisation_id if hasattr(request, 'organisation_id') else None)
        
        try:
            # Extract file_ids from request if provided
            file_ids = None
            if hasattr(request, 'file_ids') and request.file_ids:
                file_ids = list(request.file_ids)
                logger.info(f"Filtering batch search to {len(file_ids)} specific files")
            
            # Extract least_score from request if provided
            least_score = None
            if hasattr(request, 'least_score') and request.least_score > 0:
                least_score = request.least_score
                logger.info(f"Filtering batch results with minimum score: {least_score}")
            
            success, message, all_results = self.drive_service.batch_search_similar_documents(
                request.user_id,
                request.query_texts,
                request.top_k if request.top_k > 0 else 5,
                request.agent_id if hasattr(request, 'agent_id') and request.agent_id else None,
                request.organisation_id if hasattr(request, 'organisation_id') and request.organisation_id else None,
                file_ids,
                least_score
            )
            
            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return google_drive_pb2.BatchSearchSimilarDocumentsResponse(success=False)
            
            # Convert to proto models
            query_results_list = []
            
            for i, (query_text, results) in enumerate(zip(request.query_texts, all_results)):
                # Convert results for this query to proto models
                result_models = []
                for result in results:
                    # Convert entities to proto format
                    entity_protos = []
                    for entity in result.get('entities', []):
                        entity_proto = google_drive_pb2.EntityInfo(
                            id=entity.get('id', ''),
                            name=entity.get('name', ''),
                            type=entity.get('type', ''),
                            properties=convert_properties_to_string_map(entity.get('properties', {}))
                        )
                        entity_protos.append(entity_proto)
                    
                    # Convert relationships to proto format
                    relationship_protos = []
                    for relationship in result.get('relationships', []):
                        relationship_proto = google_drive_pb2.RelationshipInfo(
                            id=relationship.get('id', ''),
                            type=relationship.get('type', ''),
                            source_entity_id=relationship.get('source_entity_id', ''),
                            target_entity_id=relationship.get('target_entity_id', ''),
                            properties=convert_properties_to_string_map(relationship.get('properties', {}))
                        )
                        relationship_protos.append(relationship_proto)
                    
                    result_model = google_drive_pb2.SearchResultItem(
                        file_id=result['file_id'],
                        file_name=result['file_name'],
                        mime_type=result.get('mime_type', ''),
                        web_view_link=result.get('web_view_link', ''),
                        created_time=result['created_time'],
                        modified_time=result['modified_time'],
                        score=float(result['score']),
                        vector_id=result['vector_id'],
                        chunk_text=result.get('chunk_text', ''),  # Include the chunk text for LLM context
                        search_type=result.get('search_type', 'hybrid'),
                        entities=entity_protos,
                        relationships=relationship_protos
                    )
                    result_models.append(result_model)
                
                # Create QueryResults for this query
                query_results = google_drive_pb2.QueryResults(
                    query_text=query_text,
                    results=result_models
                )
                query_results_list.append(query_results)
            
            return google_drive_pb2.BatchSearchSimilarDocumentsResponse(
                success=True,
                message=message,
                query_results=query_results_list
            )
            
        except Exception as e:
            logger.error("Error in batch search for similar documents", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error in batch search for similar documents: {str(e)}")
            return google_drive_pb2.BatchSearchSimilarDocumentsResponse(success=False)
            
    def syncFileByUrl(self, request, context):
        """
        Schedule file sync by URLs to run in background worker and return immediately.
        Supports both Google Drive URLs and generic HTTP/HTTPS URLs.
        """
        logger.info("Received request to sync files by URLs",
                   drive_urls=request.drive_url,
                   agent_id=request.agent_id,
                   user_id=request.user_id if hasattr(request, 'user_id') and request.user_id else "None",
                   organisation_id=request.organisation_id)
        
        try:
            # Validate that we have URLs to process
            if not request.drive_url:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("No URLs provided")
                return google_drive_pb2.SyncFileByUrlResponse(
                    success=False,
                    message="No URLs provided",
                    synced_files=[],
                    total_files=0,
                    successful_syncs=0,
                    failed_syncs=0
                )
            
            # Validate that we have an agent ID
            if not request.agent_id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("agent_id is required")
                return google_drive_pb2.SyncFileByUrlResponse(
                    success=False,
                    message="agent_id is required",
                    synced_files=[],
                    total_files=0,
                    successful_syncs=0,
                    failed_syncs=0
                )
            
            # Validate that we have an organisation ID
            if not request.organisation_id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("organisation_id is required")
                return google_drive_pb2.SyncFileByUrlResponse(
                    success=False,
                    message="organisation_id is required",
                    synced_files=[],
                    total_files=0,
                    successful_syncs=0,
                    failed_syncs=0
                )
            
            # Schedule the job in the worker
            job_id = self.sync_worker.schedule_file_by_url_job(
                organisation_id=request.organisation_id,
                drive_urls=list(request.drive_url),
                agent_id=request.agent_id,
                user_id=request.user_id if hasattr(request, 'user_id') and request.user_id else None,
                delay_seconds=0  # Start immediately
            )
            
            logger.info(f"File sync by URL job scheduled with ID: {job_id}")
            
            # Create empty placeholder synced files for response
            synced_files = []
            for drive_url in request.drive_url:
                synced_files.append(google_drive_pb2.SyncedFileInfo(
                    file_id="",
                    file_name="",
                    drive_url=drive_url,
                    sync_status="scheduled",
                    error_message=""
                ))
            
            return google_drive_pb2.SyncFileByUrlResponse(
                success=True,
                message=f"File sync by URL job scheduled successfully. Job ID: {job_id}",
                synced_files=synced_files,
                total_files=len(request.drive_url),
                successful_syncs=0,  # Will be updated when job completes
                failed_syncs=0       # Will be updated when job completes
            )
            
        except Exception as e:
            logger.error("Error scheduling file sync by URL job", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error scheduling file sync job: {str(e)}")
            return google_drive_pb2.SyncFileByUrlResponse(
                success=False,
                message=f"Error: {str(e)}",
                synced_files=[],
                total_files=len(request.drive_url) if request.drive_url else 0,
                successful_syncs=0,
                failed_syncs=0
            )
    
    def getServiceAccountTopLevelFolders(self, request, context):
        """
        Get top-level folders from service account.
        """
        logger.info("Received request to get top-level folders from service account",
                   organisation_id=request.organisation_id)
        
        try:
            # Get drive service with appropriate credentials
            service = self.drive_service._get_drive_service(request.organisation_id)
            if not service:
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details("No Google Drive service account credentials found")
                return google_drive_pb2.GetServiceAccountTopLevelFoldersResponse(
                    success=False,
                    message="No Google Drive service account credentials found"
                )
            
            # Get top-level folders using the service account manager
            success, message, folders = self.drive_service.service_account_manager.validate_service_account_access(
                self.drive_service.get_service_account_credentials_json(request.organisation_id)
            )
            
            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return google_drive_pb2.GetServiceAccountTopLevelFoldersResponse(
                    success=False,
                    message=message
                )
            
            # Convert folders to proto models
            folder_models = []
            for folder in folders:
                folder_model = google_drive_pb2.FolderInfo(
                    id=folder['id'],
                    name=folder['name']
                )
                folder_models.append(folder_model)
            
            return google_drive_pb2.GetServiceAccountTopLevelFoldersResponse(
                success=True,
                message=f"Found {len(folder_models)} top-level folders",
                folders=folder_models
            )
            
        except Exception as e:
            logger.error("Error getting top-level folders from service account", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error getting top-level folders: {str(e)}")
            return google_drive_pb2.GetServiceAccountTopLevelFoldersResponse(
                success=False,
                message=f"Error: {str(e)}"
            )
    
    def getFileSyncByUrlStatus(self, request, context):
        """
        Get the status of a file sync by URL job.
        """
        logger.info("Received request to get file sync by URL status",
                   job_id=request.job_id)
        
        try:
            # Check if job exists in Redis
            job_data_str = self.drive_service.redis_service.get(request.job_id)
            
            if not job_data_str:
                # Check if we have a result for this job
                result_key = f"file_url_sync_result:{request.job_id}"
                result_data_str = self.drive_service.redis_service.get(result_key)
                
                if not result_data_str:
                    return google_drive_pb2.SyncFileByUrlResponse(
                        success=False,
                        message="Job not found or expired",
                        synced_files=[],
                        total_files=0,
                        successful_syncs=0,
                        failed_syncs=0,
                        sync_status="not_found"
                    )
                
                # Job has completed, return the result
                import json
                result_data = json.loads(result_data_str)
                
                # Convert synced files to proto models
                synced_files = []
                for file_info in result_data.get('synced_files', []):
                    synced_files.append(google_drive_pb2.SyncedFileInfo(
                        file_id=file_info.get('file_id', ''),
                        file_name=file_info.get('file_name', ''),
                        drive_url=file_info.get('drive_url', ''),
                        sync_status=file_info.get('sync_status', 'unknown'),
                        error_message=file_info.get('error_message', '')
                    ))
                
                return google_drive_pb2.SyncFileByUrlResponse(
                    success=result_data.get('success', False),
                    message=result_data.get('message', 'Job completed'),
                    synced_files=synced_files,
                    total_files=result_data.get('total_files', 0),
                    successful_syncs=result_data.get('successful_syncs', 0),
                    failed_syncs=result_data.get('failed_syncs', 0),
                    sync_status="completed"
                )
            
            # Job exists but hasn't been processed yet
            import json
            job_data = json.loads(job_data_str)
            
            # Check if job is still in queue (pending)
            queue_score = self.drive_service.redis_service.zscore("gdrive_sync_queue", request.job_id)
            
            if queue_score is not None:
                # Create empty placeholder synced files for response
                synced_files = []
                for drive_url in job_data.get('drive_urls', []):
                    synced_files.append(google_drive_pb2.SyncedFileInfo(
                        file_id="",
                        file_name="",
                        drive_url=drive_url,
                        sync_status="scheduled",
                        error_message=""
                    ))
                
                return google_drive_pb2.SyncFileByUrlResponse(
                    success=True,
                    message="File sync job is pending in queue",
                    synced_files=synced_files,
                    total_files=len(job_data.get('drive_urls', [])),
                    successful_syncs=0,
                    failed_syncs=0,
                    sync_status="pending"
                )
            else:
                # Job has been removed from queue but no result yet (in progress)
                # Create empty placeholder synced files for response
                synced_files = []
                for drive_url in job_data.get('drive_urls', []):
                    synced_files.append(google_drive_pb2.SyncedFileInfo(
                        file_id="",
                        file_name="",
                        drive_url=drive_url,
                        sync_status="in_progress",
                        error_message=""
                    ))
                
                return google_drive_pb2.SyncFileByUrlResponse(
                    success=True,
                    message="File sync job is in progress",
                    synced_files=synced_files,
                    total_files=len(job_data.get('drive_urls', [])),
                    successful_syncs=0,
                    failed_syncs=0,
                    sync_status="in_progress"
                )
                
        except Exception as e:
            logger.error("Error getting file sync by URL status", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error getting file sync status: {str(e)}")
            return google_drive_pb2.SyncFileByUrlResponse(
                success=False,
                message=f"Error: {str(e)}",
                synced_files=[],
                total_files=0,
                successful_syncs=0,
                failed_syncs=0,
                sync_status="error"
            )

    def initiateOAuth(self, request, context):
        """
        Initiate Google OAuth flow for source creation.
        """
        logger.info("Received request to initiate OAuth flow",
                   organisation_id=request.organisation_id,
                   source_name=request.source_name if hasattr(request, 'source_name') else None)
        
        try:
            from app.utils.google_oauth_manager import GoogleOAuthManager
            from app.modules.organisation.services.source import SourceService
            from app.utils.constants.sources import SourceType
            from app.db.neo4j import execute_read_query
            
            # Check if Google Drive source already exists for this organization
            source_service = SourceService()
            
            # Check if a different auth type already exists for this source type
            conflicting_auth_query = source_service.queries.CHECK_EXISTING_SOURCE_BY_AUTH_TYPE
            conflicting_auth_params = {
                "org_id": request.organisation_id,
                "source_type": SourceType.GOOGLE_DRIVE.value,
                "auth_type": "oauth"
            }

            conflicting_result = execute_read_query(conflicting_auth_query, conflicting_auth_params)

            if conflicting_result:
                logger.error(f"Google Drive source with different auth type already exists for organization {request.organisation_id}")
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details("Google Drive source with service account authentication already exists. Only one authentication type per organization is allowed.")
                return google_drive_pb2.InitiateOAuthResponse(
                    success=False,
                    message="Google Drive source with service account authentication already exists. Only one authentication type per organization is allowed.",
                    oauth_url="",
                    state=""
                )

            # Check if organization already has a Google Drive source
            existing_query = source_service.queries.CHECK_EXISTING_SOURCE
            existing_params = {
                "org_id": request.organisation_id,
                "source_type": SourceType.GOOGLE_DRIVE.value
            }
            
            existing_result = execute_read_query(existing_query, existing_params)
            if existing_result:
                logger.error(f"Google Drive source already exists for organization {request.organisation_id}")
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details("Google Drive source already exists for this organization")
                return google_drive_pb2.InitiateOAuthResponse(
                    success=False,
                    message="Google Drive source already exists for this organization",
                    oauth_url="",
                    state=""
                )
            
            oauth_manager = GoogleOAuthManager()
            
            # Generate OAuth URL and state
            success, oauth_url, state = oauth_manager.generate_oauth_url(
                organisation_id=request.organisation_id
            )
            
            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(oauth_url)  # oauth_url contains error message when success=False
                return google_drive_pb2.InitiateOAuthResponse(
                    success=False,
                    message=oauth_url,
                    oauth_url="",
                    state=""
                )
            
            return google_drive_pb2.InitiateOAuthResponse(
                success=True,
                message="OAuth flow initiated successfully",
                oauth_url=oauth_url,
                state=state
            )
            
        except Exception as e:
            logger.error("Error initiating OAuth flow", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error initiating OAuth flow: {str(e)}")
            return google_drive_pb2.InitiateOAuthResponse(
                success=False,
                message=f"Error: {str(e)}",
                oauth_url="",
                state=""
            )

    def completeOAuth(self, request, context):
        """
        Complete Google OAuth flow and create OAuth source.
        """
        logger.info("Received request to complete OAuth flow",
                   code=request.code[:20] + "..." if len(request.code) > 20 else request.code,
                   state=request.state)
        
        try:
            from app.utils.google_oauth_manager import GoogleOAuthManager
            from app.modules.organisation.services.source import SourceService
            
            oauth_manager = GoogleOAuthManager()
            source_service = SourceService()
            
            # Complete OAuth flow and get tokens
            success, message, result = oauth_manager.handle_oauth_callback(
                code=request.code,
                state=request.state
            )
            
            if not success:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(message)
                return google_drive_pb2.CompleteOAuthResponse(
                    success=False,
                    message=message
                )
            
            if not result:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("No token data received")
                return google_drive_pb2.CompleteOAuthResponse(
                    success=False,
                    message="No token data received"
                )
            
            # Extract data from result
            organisation_id = result['organisation_id']
            source_name = result.get('source_name', 'Google Drive OAuth')
            user_email = result['user_email']
            
            # Create OAuth source using the private method
            source_response = source_service._create_oauth_source(result)
            
            success, source_model = source_response
            
            if not success:
                error_message = source_model.get('error', 'Failed to create OAuth source') if isinstance(source_model, dict) else 'Failed to create OAuth source'
                
                # Set appropriate status code based on error type
                if 'already exists' in error_message.lower():
                    context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                else:
                    context.set_code(grpc.StatusCode.INTERNAL)
                    
                context.set_details(error_message)
                return google_drive_pb2.CompleteOAuthResponse(
                    success=False,
                    message=error_message
                )
            
            # Schedule automatic sync for the new OAuth source
            sync_message = ""
            try:
                from app.modules.connectors.google_drive.services.google_drive_service import GoogleDriveService
                gdrive_service = GoogleDriveService()
                
                job_id = gdrive_service._schedule_sync(
                    user_id=organisation_id,
                    organisation_id=organisation_id,
                    full_sync=True,
                    delay_seconds=5
                )
                
                logger.info(f"Automatic sync job scheduled for new OAuth source",
                           organisation_id=organisation_id,
                           job_id=job_id)
                sync_message = f" Automatic sync job scheduled (ID: {job_id})."
                
            except Exception as sync_error:
                logger.warning(f"Failed to schedule automatic sync for OAuth source: {str(sync_error)}")
                sync_message = " Note: Automatic sync scheduling failed, manual sync may be required."

            idx = source_model.id if hasattr(source_model, 'id') else ''
            organisation_idx = source_model.organisation_id if hasattr(source_model, 'organisation_id') else organisation_id
            namex = source_model.name if hasattr(source_model, 'name') else source_name
            creates_atx = source_model.created_at if hasattr(source_model, 'created_at') else ''
            updated_atx = source_model.updated_at if hasattr(source_model, 'updated_at') else ''

            # Convert source info to proto
            source_info = google_drive_pb2.DriveSourceInfo(
                id=idx,
                organisation_id=organisation_idx,
                name=namex,
                auth_type="oauth",
                user_email=user_email,
                created_at=creates_atx,
                updated_at=updated_atx
            )
            return google_drive_pb2.CompleteOAuthResponse(
                success=True,
                message=f"OAuth source created successfully.{sync_message}",
                source=source_info,
                synced_files=[]  # No files synced during source creation
            )
            
        except Exception as e:
            logger.error("Error completing OAuth flow", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error completing OAuth flow: {str(e)}")
            return google_drive_pb2.CompleteOAuthResponse(
                success=False,
                message=f"Error: {str(e)}"
            )

    def refreshOAuthToken(self, request, context):
        """
        Refresh OAuth access token.
        """
        logger.info("Received request to refresh OAuth token",
                   organisation_id=request.organisation_id)
        
        try:
            from app.utils.google_oauth_manager import GoogleOAuthManager
            
            oauth_manager = GoogleOAuthManager()
            
            # Refresh token
            success, message, result = oauth_manager.refresh_access_token(
                organisation_id=request.organisation_id
            )
            
            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return google_drive_pb2.RefreshTokenResponse(
                    success=False,
                    message=message,
                    expires_at=""
                )
            
            if not result:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("No token data received")
                return google_drive_pb2.RefreshTokenResponse(
                    success=False,
                    message="No token data received",
                    expires_at=""
                )
            
            return google_drive_pb2.RefreshTokenResponse(
                success=True,
                message="Token refreshed successfully",
                expires_at=result.get('expires_at', '')
            )
            
        except Exception as e:
            logger.error("Error refreshing OAuth token", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error refreshing OAuth token: {str(e)}")
            return google_drive_pb2.RefreshTokenResponse(
                success=False,
                message=f"Error: {str(e)}",
                expires_at=""
            )

    def revokeOAuthToken(self, request, context):
        """
        Revoke OAuth tokens and delete OAuth source.
        """
        logger.info("Received request to revoke OAuth token",
                   organisation_id=request.organisation_id)
        
        try:
            from app.utils.google_oauth_manager import GoogleOAuthManager
            
            oauth_manager = GoogleOAuthManager()
            
            # Revoke token and delete source
            success, message = oauth_manager.revoke_token(
                organisation_id=request.organisation_id
            )
            
            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return google_drive_pb2.RevokeTokenResponse(
                    success=False,
                    message=message
                )
            
            return google_drive_pb2.RevokeTokenResponse(
                success=True,
                message=message
            )
            
        except Exception as e:
            logger.error("Error revoking OAuth token", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error revoking OAuth token: {str(e)}")
            return google_drive_pb2.RevokeTokenResponse(
                success=False,
                message=f"Error: {str(e)}"
            )