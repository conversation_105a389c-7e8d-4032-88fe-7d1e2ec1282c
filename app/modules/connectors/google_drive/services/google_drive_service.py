import os
import json
import time
from datetime import datetime, timedelta
import structlog
from typing import Tu<PERSON>, List, Dict, Any, Optional
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from app.core.config import settings
from app.db.neo4j import execute_write_query, execute_read_query
from app.utils.redis.redis_service import RedisService
from app.utils.pinecone.pinecone_service import PineconeService
from app.utils.search.hybrid_search_engine import HybridSearchEngine
from app.utils.file_processing.generic_file_service import GenericFileService
from app.utils.source_credentials import get_service_account_credentials_legacy
from app.utils.google_service_account import GoogleServiceAccountManager
from app.modules.connectors.google_drive.repository.google_drive_queries import (
    GoogleDriveUserQueries,
    GoogleDriveFileQueries,
    GoogleDriveFolderQueries,
    GoogleDriveListQueries,
    GoogleDriveSyncQueries,
    GoogleDriveRelationshipQueries,
    GoogleDriveMetadataQueries,
    GoogleDriveServiceAccountQueries
)
import uuid
logger = structlog.get_logger()

class GoogleDriveService:
    """
    Service for Google Drive integration operations.
    """

    def __init__(self):
        self.redis_service = RedisService()
        self.pinecone_service = PineconeService()
        self.scopes = [
    'https://www.googleapis.com/auth/drive.readonly',
     'https://www.googleapis.com/auth/drive']

        # Initialize repository instances
        self.user_queries = GoogleDriveUserQueries()
        self.file_queries = GoogleDriveFileQueries()
        self.folder_queries = GoogleDriveFolderQueries()
        self.list_queries = GoogleDriveListQueries()
        self.sync_queries = GoogleDriveSyncQueries()
        self.relationship_queries = GoogleDriveRelationshipQueries()
        self.metadata_queries = GoogleDriveMetadataQueries()
        self.service_account_queries = GoogleDriveServiceAccountQueries()
        
        # Initialize service account manager
        self.service_account_manager = GoogleServiceAccountManager()
        
    def extract_file_id_from_url(self, drive_url: str) -> Optional[str]:
        """
        Extract file ID from a Google Drive URL.
        
        Args:
            drive_url: The Google Drive URL

        Returns:
            The file ID or None if extraction failed
        """
        try:
            # Handle different URL formats
            # Format 1: https://drive.google.com/file/d/{fileId}/view
            # Format 2: https://drive.google.com/open?id={fileId}
            # Format 3: https://docs.google.com/document/d/{fileId}/edit
            # Format 4: https://drive.google.com/drive/folders/{fileId}

            if "drive.google.com/file/d/" in drive_url:
                # Extract ID from /file/d/{fileId}/view format
                file_id = drive_url.split("/file/d/")[1].split("/")[0]
            elif "drive.google.com/open?id=" in drive_url:
                # Extract ID from ?id={fileId} format
                file_id = drive_url.split("id=")[1].split("&")[0]
            elif "docs.google.com" in drive_url:
                # Extract ID from docs.google.com/*/d/{fileId}/ format
                file_id = drive_url.split("/d/")[1].split("/")[0]
            elif "drive.google.com/drive/folders/" in drive_url:
                # Extract ID from drive.google.com/drive/folders/{fileId}
                # format
                file_id = drive_url.split("/folders/")[1].split("/")[0]
            else:
                logger.error(
                    f"Unsupported Google Drive URL format: {drive_url}")
                return None

            return file_id
        except Exception as e:
            logger.error(f"Error extracting file ID from URL: {str(e)}")
            return None

    def sync_file_by_id(self,
    file_id: str,
    agent_id: str,
    user_id: Optional[str],
    organisation_id: str,
    url: Optional[str] = None) -> Tuple[bool,
    str,
    Dict[str,
     Any]]:
        """
        Sync a specific file by ID from Google Drive or by URL from any HTTP/HTTPS source.

        Args:
            file_id: The ID of the file to sync (for Google Drive) or generated ID (for generic URLs)
            agent_id: The ID of the agent
            user_id: Optional ID of the user
            organisation_id: The ID of the organization
            url: Optional URL for generic file processing

        Returns:
            Tuple containing:
            - success: Boolean indicating if sync was successful
            - message: Status message
            - file_data: Dictionary with file details
        """
        try:
            # Create a virtual user ID for the service account if user_id not provided
            effective_user_id = user_id if user_id else f"sa_{organisation_id}"

            # Determine if this is a Google Drive file or generic URL
            if url:
                # Handle generic URL processing
                generic_service = GenericFileService()
                
                # Process the file from URL
                success, message, file_data = generic_service.process_file_from_url(
                    url=url,
                    file_id=file_id,
                    user_id=effective_user_id,
                    organisation_id=organisation_id
                )
                
                if not success:
                    return False, message, {}
                
                # Create or update the file in Neo4j using the processed data
                self._create_or_update_generic_file(
                    effective_user_id, file_data, organisation_id, agent_id)
                
            else:
                # Handle Google Drive file processing with enhanced chunking and knowledge graph extraction
                service = self._get_drive_service(organisation_id)
                if not service:
                    return False, "Failed to get authenticated drive service", {}

                # Get file metadata from Google Drive
                file_data = service.files().get(
                    fileId=file_id,
                    fields="id, name, mimeType, parents, createdTime, modifiedTime, size, webViewLink, permissions(emailAddress, role)"
                ).execute()

                # Create or update the file in Neo4j with enhanced processing (same as sync drive)
                self._create_or_update_file(
                    effective_user_id, file_data, service, organisation_id)

            # Create mandatory relationships
            self._create_file_relationships(file_id, agent_id, user_id, organisation_id)

            # Return success with file details
            return True, f"File {file_data['name']} synced successfully", {
                'id': file_id,
                'name': file_data['name'],
                'mime_type': file_data.get('mimeType', file_data.get('mime_type', '')),
                'web_view_link': file_data.get('webViewLink', file_data.get('web_view_link', ''))
            }

        except Exception as e:
            logger.error(f"Error syncing file: {str(e)}")
            return False, f"Error syncing file: {str(e)}", {}

    def sync_drive(self, organisation_id: str,
                   full_sync: bool = False) -> Tuple[bool, str, int, int]:
        """
        Sync Google Drive files and folders using appropriate credentials (service account or OAuth).

        Args:
            organisation_id: The ID of the organization
            full_sync: Whether to perform a full sync

        Returns:
            Tuple containing:
            - success: Boolean indicating if sync was successful
            - message: Status message
            - files_synced: Number of files synced
            - folders_synced: Number of folders synced
        """
        try:
            # Determine auth type by checking what credentials are available
            auth_type = self._get_source_auth_type(organisation_id)
            logger.info(f"Using {auth_type} credentials for Google Drive sync for organization {organisation_id}")
            if auth_type == "oauth":
                # Use OAuth credentials
                from app.utils.google_oauth_manager import GoogleOAuthManager
                oauth_manager = GoogleOAuthManager()
                
                credentials = oauth_manager.get_oauth_credentials(organisation_id)
                if not credentials:
                    return False, "Failed to get OAuth credentials", 0, 0
                
                # Build Drive service with OAuth credentials
                service = build('drive', 'v3', credentials=credentials)
                
                logger.info(f"syncing _sync_files_and_folders_oauth")
                # Start syncing using OAuth
                files_synced, folders_synced = self._sync_files_and_folders_oauth(
                    service, organisation_id, full_sync
                )
                print("Files synced:", files_synced, "Folders synced:", folders_synced)
                return True, "Google Drive sync completed successfully (OAuth)", files_synced, folders_synced
                
            else:
                # Use service account credentials (default)
                logger.info(f"Using appropriate credentials for Google Drive sync for organization {organisation_id}")
                service = self._get_drive_service(organisation_id)
                if not service:
                    return False, "Failed to get service account credentials", 0, 0

                # Start syncing using service account
                files_synced, folders_synced = self._sync_files_and_folders_service_account(
                    service, organisation_id, full_sync
                )

                return True, "Google Drive sync completed successfully (Service Account)", files_synced, folders_synced

        except HttpError as error:
            logger.error(f"Google Drive API error: {error}")
            return False, f"Google Drive API error: {str(error)}", 0, 0
        except Exception as e:
            logger.error(f"Error syncing Google Drive: {str(e)}")
            return False, f"Error syncing Google Drive: {str(e)}", 0, 0

    def _get_source_auth_type(self, organisation_id: str) -> str:
        """
        Determine the authentication type for the organization's Google Drive source.
        
        Args:
            organisation_id: The ID of the organization
            
        Returns:
            str: "oauth" or "service_account"
        """
        try:
            # Query to get source auth_type
            query = """
            MATCH (o:Organisation {id: $org_id})-[:HAS_SOURCE]->(s:Source)
            WHERE s.type = 'google_drive'
            RETURN s.auth_type as auth_type
            """
            
            result = execute_read_query(query, {"org_id": organisation_id})
            
            if result and len(result) > 0:
                auth_type = result[0].get('auth_type')
                if auth_type == 'oauth':
                    return 'oauth'
            
            # Default to service account if not found or not oauth
            return 'service_account'
            
        except Exception as e:
            logger.error(f"Error determining auth type for organization {organisation_id}: {str(e)}")
            return 'service_account'  # Default fallback

    def _sync_files_and_folders_oauth(self, service, organisation_id: str, full_sync: bool = False) -> Tuple[int, int]:
        """
        Sync files and folders using OAuth credentials.
        
        Args:
            service: Google Drive service instance with OAuth credentials
            organisation_id: The ID of the organization
            full_sync: Whether to perform a full sync
            
        Returns:
            Tuple of (files_synced, folders_synced)
        """
        try:
            logger.info(f"Starting OAuth-based Google Drive sync for organization {organisation_id}")
            
            # For now, use the same sync logic as service account
            # In the future, this could be customized for OAuth-specific behavior
            return self._sync_files_and_folders_service_account(service, organisation_id, full_sync)
            
        except Exception as e:
            logger.error(f"Error in OAuth sync for organization {organisation_id}: {str(e)}")
    def _get_drive_service(self, organisation_id: str):
        """
        Get Google Drive service with appropriate credentials (OAuth or service account).
        
        Args:
            organisation_id: The ID of the organization
            
        Returns:
            Google Drive service instance or None if credentials not available
        """
        try:
            auth_type = self._get_source_auth_type(organisation_id)
            
            if auth_type == "oauth":
                # Use OAuth credentials
                from app.utils.google_oauth_manager import GoogleOAuthManager
                oauth_manager = GoogleOAuthManager()
                
                credentials = oauth_manager.get_oauth_credentials(organisation_id)
                if not credentials:
                    logger.error(f"Failed to get OAuth credentials for organization {organisation_id}")
                    return None
                
                # Build Drive service with OAuth credentials
                return build('drive', 'v3', credentials=credentials)
                
            else:
                # Use service account credentials (default)
                return self.get_service_account_drive_service(organisation_id)
                
        except Exception as e:
            logger.error(f"Error getting Drive service for organization {organisation_id}: {str(e)}")
            return None

            raise e



    def fetch_top_level_folders(
        self, organisation_id: str) -> Tuple[bool, str, List[Dict[str, str]]]:
        """
        Fetch only top-level folders from Google Drive using service account.

        Args:
            organisation_id: The ID of the organization

        Returns:
            Tuple containing:
            - success: Boolean indicating if operation was successful
            - message: Status message
            - folders: List of folder objects with id and name
        """
        try:
            # Get drive service with appropriate credentials (OAuth or service account)
            service = self._get_drive_service(organisation_id)
            if not service:
                return False, "Failed to get service account credentials", []

            # Query for top-level folders (those in the root or shared with the service account)
            query = "mimeType = 'application/vnd.google-apps.folder' and (('root' in parents) or (sharedWithMe = true)) and trashed = false"
            fields = "nextPageToken, files(id, name, parents, createdTime, modifiedTime, owners, permissions(emailAddress, role, type))"

            folders = []
            page_token = None

            while True:
                response = service.files().list(
                    q=query,
                    spaces='drive',
                    fields=fields,
                    pageToken=page_token,
                    pageSize=100
                ).execute()

                items = response.get('files', [])

                # Process folders and create Neo4j nodes with permissions
                for folder in items:
                    # Create or update folder in Neo4j with permissions
                    self._create_or_update_folder_with_permissions(
                        folder, organisation_id)

                    # Process permissions to create users and relationships
                    self._process_folder_permissions(folder, organisation_id)

                    # Add to result list with full permission data
                    folders.append({
                        'id': folder['id'],
                        'name': folder['name'],
                        'permissions': folder.get('permissions', []),
                        'created_time': folder.get('createdTime', ''),
                        'modified_time': folder.get('modifiedTime', ''),
                        'owners': folder.get('owners', []),
                        'permissions_count': len(folder.get('permissions', []))
                    })

                # Get next page token
                page_token = response.get('nextPageToken')
                if not page_token:
                    break

            return True, f"Found {len(folders)} top-level folders", folders

        except Exception as e:
            logger.error(f"Error fetching top-level folders: {str(e)}")
            return False, f"Error fetching top-level folders: {str(e)}", []

    def disconnect_drive(self, organisation_id: str) -> Tuple[bool, str]:
        """
        Disconnect Google Drive for an organization (remove all synced data).

        Args:
            organisation_id: The ID of the organization

        Returns:
            Tuple containing:
            - success: Boolean indicating if disconnection was successful
            - message: Status message
        """
        try:
            # Remove all Google Drive files for this organization
            files_query = self.file_queries.DELETE_ORGANIZATION_FILES
            execute_write_query(files_query,
     {'organisation_id': organisation_id})

            # Remove all Google Drive folders for this organization
            folders_query = self.folder_queries.DELETE_ORGANIZATION_FOLDERS
            execute_write_query(
    folders_query, {
        'organisation_id': organisation_id})

            # Cancel scheduled syncs for this organization
            self._cancel_scheduled_syncs_for_organization(organisation_id)

            return True, "Google Drive disconnected successfully"

        except Exception as e:
            logger.error(f"Error disconnecting Google Drive: {str(e)}")
            return False, f"Error disconnecting Google Drive: {str(e)}"

    def _schedule_sync(
    self,
    user_id: str,
    organisation_id: str = None,
    full_sync: bool = False,
     delay_seconds: int = 0) -> str:
        """
        Schedule a Google Drive sync job.

        Args:
            user_id: The ID of the user
            organisation_id: The ID of the organisation (required)
            full_sync: Whether to perform a full sync
            delay_seconds: Delay before executing the job

        Returns:
            The job ID
            
        Raises:
            ValueError: If organisation_id is None
        """
        # organisation_id is required for sync jobs
        if organisation_id is None:
            raise ValueError("organisation_id is required for scheduling sync jobs")
            
        job_data = {
            'user_id': user_id,
            'organisation_id': organisation_id,
            'full_sync': full_sync,
            'scheduled_at': (datetime.utcnow() + timedelta(seconds=delay_seconds)).isoformat()
        }

        # Store in Redis with appropriate expiration
        job_id = f"gdrive_sync:{user_id}:{int(time.time())}"
        self.redis_service.set(
    job_id,
    json.dumps(job_data),
     ex=86400)  # 24 hour expiration

        #print("Job id created", job_id)

        # Add to sorted set for processing
        score = time.time() + delay_seconds
        self.redis_service.zadd("gdrive_sync_queue", {job_id: score})

        return job_id

    def _cancel_scheduled_syncs(self, user_id: str) -> None:
        """
        Cancel all scheduled syncs for a user.

        Args:
            user_id: The ID of the user
        """
        # Find all sync jobs for this user
        pattern = f"gdrive_sync:{user_id}:*"
        keys = self.redis_service.keys(pattern)

        # Remove from sorted set and delete keys
        for key in keys:
            self.redis_service.zrem("gdrive_sync_queue", key)
            self.redis_service.delete(key)

    def _create_or_update_folder(self,
    user_id: str,
    folder_data: Dict[str,
    Any],
     organisation_id: str = None) -> None:
        """
        Create or update a Google Drive folder in Neo4j.

        Args:
            user_id: The ID of the user
            folder_data: The folder data from Google Drive API
            organisation_id: The ID of the organisation that owns this folder
        """
        # Get organisation_id from user if not provided
        if not organisation_id:
            org_result = execute_read_query(
    self.relationship_queries.GET_USER_ORGANISATION, {
        'user_id': user_id})
            if org_result and org_result[0].get('organisation_id'):
                organisation_id = org_result[0]['organisation_id']
            else:
                logger.error(
                    f"Could not determine organisation_id for user {user_id}")
                return
        # Extract shared users
        shared_with = []
        #print(folder_data)
        if 'permissions' in folder_data:
            for perm in folder_data['permissions']:
                #print("Printing permissions here")
                #print(folder_data['name'], perm)
                if 'emailAddress' in perm and perm['role'] != 'owner':
                    shared_with.append(perm['emailAddress'])

        # Create or update folder node using repository
        query = self.folder_queries.CREATE_OR_UPDATE_FOLDER
        params = {
            'user_id': user_id,
            'organisation_id': organisation_id,
            'folder_id': folder_data['id'],
            'name': folder_data['name'],
            'created_time': folder_data['createdTime'],
            'modified_time': folder_data['modifiedTime'],
            'permissions': json.dumps(folder_data.get('permissions', []))
        }

        execute_write_query(query, params)

        # Store additional properties that aren't in the schema
        additional_params = {
            'folder_id': folder_data['id'],
            'parent_ids': folder_data.get('parents', []),
            'shared_with': shared_with,
            'current_time': datetime.utcnow().isoformat(),
            'organisation_id': organisation_id
        }

        execute_write_query(
    self.metadata_queries.UPDATE_FOLDER_ADDITIONAL_PROPERTIES,
     additional_params)
        #print("folder created ", folder_data['name'])

        # Create folder-folder relationships immediately after folder creation
        self._create_folder_folder_relationships_immediate(folder_data, organisation_id)

        # Invalidate the access cache for this user since folder access has been updated
        # This is important because folder access affects access to all files
        # within the folder
        self.pinecone_service.invalidate_access_cache(user_id)

        # If the folder is shared with other users, invalidate their caches too
        if shared_with:
            # Find user IDs from email addresses using repository
            for email in shared_with:
                user_query = self.sync_queries.FIND_USER_BY_EMAIL
                user_result = execute_read_query(user_query, {'email': email})
                if user_result and user_result[0].get('user_id'):
                    self.pinecone_service.invalidate_access_cache(
                        user_result[0]['user_id'])

    def _create_or_update_file(self,
    user_id: str,
    file_data: Dict[str,
    Any],
    service=None,
     organisation_id: str = None) -> None:
        """
        Create or update a Google Drive file in Neo4j and Pinecone.

        Args:
            user_id: The ID of the user
            file_data: The file data from Google Drive API
            service: The Google Drive API service (optional, for text extraction)
            organisation_id: The ID of the organisation that owns this file
        """
        # Get organisation_id from user if not provided
        if not organisation_id:
            org_result = execute_read_query(
    self.relationship_queries.GET_USER_ORGANISATION, {
        'user_id': user_id})
            if org_result and org_result[0].get('organisation_id'):
                organisation_id = org_result[0]['organisation_id']
            else:
                logger.error(
                    f"Could not determine organisation_id for user {user_id}")
                return
        # Extract shared users
        shared_with = []
        if 'permissions' in file_data:
            for perm in file_data['permissions']:
                if 'emailAddress' in perm and perm['role'] != 'owner':
                    shared_with.append(perm['emailAddress'])

        # Create or update file node using repository
        query = self.file_queries.CREATE_OR_UPDATE_FILE
        params = {
            'user_id': user_id,
            'organisation_id': organisation_id,
            'agent_id': None,
            'file_id': file_data['id'],
            'name': file_data['name'],
            'mime_type': file_data['mimeType'],
            'size': file_data.get('size', 0),
            'web_view_link': file_data.get('webViewLink', ''),
            'created_time': file_data['createdTime'],
            'modified_time': file_data['modifiedTime'],
            'vector_id': None,  # Will be updated later if vectorized
            'content_hash': None,  # Could be added later
            'permissions': json.dumps(file_data.get('permissions', []))
        }

        execute_write_query(query, params)

        # Store additional properties that aren't in the schema
        additional_params = {
            'file_id': file_data['id'],
            'parent_ids': file_data.get('parents', []),
            'shared_with': shared_with,
            'current_time': datetime.utcnow().isoformat(),
            'organisation_id': organisation_id
        }

        execute_write_query(
    self.metadata_queries.UPDATE_FILE_ADDITIONAL_PROPERTIES,
     additional_params)
        #print("file created ", file_data['name'])

        # Create folder-file relationships immediately after file creation
        self._create_file_folder_relationships_immediate(file_data, organisation_id)

        # Invalidate the access cache for this user since file access has been
        # updated
        self.pinecone_service.invalidate_access_cache(user_id)

        # If the file is shared with other users, invalidate their caches too
        if shared_with:
            # Find user IDs from email addresses using repository
            for email in shared_with:
                user_query = self.sync_queries.FIND_USER_BY_EMAIL
                user_result = execute_read_query(user_query, {'email': email})
                if user_result and user_result[0].get('user_id'):
                    self.pinecone_service.invalidate_access_cache(
                        user_result[0]['user_id'])

        # Process file for Pinecone if service is provided and Pinecone is
        # initialized
        if service and self.pinecone_service.is_initialized():
            try:
                # Check if file is already vectorized and get last modified
                # time
                check_result = execute_read_query(
    self.metadata_queries.CHECK_FILE_VECTORIZATION_STATUS, {
        'file_id': file_data['id']})

                # Get current file's modified time
                current_modified_time = file_data['modifiedTime']

                # Check if file needs to be vectorized:
                # 1. If it has never been vectorized (no vector_id)
                # 2. If the file has been modified since last vectorization
                needs_vectorization = True

                if check_result and check_result[0].get('f.vector_id'):
                    last_vectorized_modified_time = check_result[0].get(
                        'f.last_vectorized_modified_time')

                    if last_vectorized_modified_time and last_vectorized_modified_time == current_modified_time:
                        # File hasn't changed since last vectorization
                        needs_vectorization = False
                        logger.info(
                            f"File {file_data['name']} hasn't changed since last vectorization, skipping")

                if needs_vectorization:
                    # Extract text from file
                    text = self.pinecone_service.extract_text_from_file(
                        file_data, service)

                    if text:
                        # If file was previously vectorized, we need to update
                        # the existing vector
                        existing_vector_id = check_result[0].get(
                            'f.vector_id') if check_result else None

                        # Upload to Pinecone with entity extraction (update if vector_id exists)
                        success, message, vector_id = self.pinecone_service.enhanced_upload_file_to_pinecone(
                            user_id, organisation_id, file_data, text, existing_vector_id
                        )

                        if success and vector_id:
                            # Update Neo4j with vector ID using repository
                            update_query = self.file_queries.UPDATE_FILE_VECTOR_ID
                            update_params = {
                                'file_id': file_data['id'],
                                'vector_id': vector_id
                            }
                            execute_write_query(update_query, update_params)

                            # Update additional vectorization metadata
                            metadata_params = {
                                'file_id': file_data['id'],
                                'vectorized_at': datetime.utcnow().isoformat(),
                                'modified_time': current_modified_time
                            }

                            execute_write_query(
    self.metadata_queries.UPDATE_FILE_VECTORIZATION_METADATA, metadata_params)

                            if existing_vector_id:
                                logger.info(
                                    f"File {file_data['name']} updated in Pinecone with new content")
                            else:
                                logger.info(
                                    f"File {file_data['name']} vectorized and stored in Pinecone")
                        else:
                            logger.warning(
                                f"Failed to vectorize file {file_data['name']}: {message}")
                    else:
                        logger.info(
                            f"No text extracted from file {file_data['name']}")

            except Exception as e:
                logger.error(f"Error processing file for Pinecone: {str(e)}")

    def _create_folder_relationships(
    self,
    user_id: str,
     organisation_id: str = None) -> None:
        """
        Create parent-child relationships between folders and files.

        Args:
            user_id: The ID of the user
            organisation_id: The ID of the organisation
        """
        # Get organisation_id from user if not provided
        if not organisation_id:
            org_result = execute_read_query(
    self.relationship_queries.GET_USER_ORGANISATION, {
        'user_id': user_id})
            if org_result and org_result[0].get('organisation_id'):
                organisation_id = org_result[0]['organisation_id']
            else:
                logger.error(
                    f"Could not determine organisation_id for user {user_id}")
                return
        # Create relationships between folders using repository
        folder_query = self.sync_queries.CREATE_FOLDER_HIERARCHY_RELATIONSHIPS
        #print("Creating folder relationships")
        execute_write_query(folder_query, {'organisation_id': organisation_id})
        #print("Folder relationships created")

        # Create relationships between folders and files using repository
        file_query = self.sync_queries.CREATE_FILE_FOLDER_RELATIONSHIPS
        #print("creating file relationships")
        execute_write_query(file_query, {'organisation_id': organisation_id})
        #print("File relationships created")

        # Create shared access relationships for other users
        # Modified to create users if they don't exist and add creation_type field
        # Corrected the position of the WHERE NOT EXISTS clause
        # Delete inherited access relationships using repository
        delete_query = self.sync_queries.DELETE_INHERITED_ACCESS_RELATIONSHIPS
        execute_write_query(delete_query, {})

        # Create shared access relationships using repository
        shared_query = self.sync_queries.CREATE_SHARED_ACCESS_RELATIONSHIPS
        params = {
            'current_time': datetime.utcnow().isoformat(),
            'organisation_id': organisation_id
        }

        #print("Creating shared access relationships")
        execute_write_query(shared_query, params)
        #print("Shared access relationships created")

        # Invalidate access caches for all users since folder relationships have changed
        # This affects which files users can access through folder hierarchy

        # Get all users who have access to any folder or file using repository
        users_query = self.sync_queries.GET_ALL_USERS_WITH_ACCESS
        users_result = execute_read_query(users_query, {})
        if users_result:
            for record in users_result:
                user_id = record.get('user_id')
                if user_id:
                    try:
                        self.pinecone_service.invalidate_access_cache(user_id)
                    except Exception as e:
                        logger.error(
                            f"Error invalidating cache for user {user_id}: {str(e)}")

    def list_files(self,
    user_id: str,
    folder_id: Optional[str] = None,
    page: int = 1,
    page_size: int = 50) -> Tuple[List[Dict[str,
    Any]],
    int,
    int,
     int]:
        """
        List Google Drive files and folders for a user.

        Args:
            user_id: The ID of the user
            folder_id: The ID of the folder to list files from
            page: The page number
            page_size: The number of items per page

        Returns:
            Tuple containing:
            - files: List of file data
            - total_count: Total number of files
            - page: The current page number
            - page_size: The number of items per page
        """
        # Calculate skip for pagination
        skip = (page - 1) * page_size

        # Use repository queries
        if folder_id:
            # List files in a specific folder
            query = self.list_queries.LIST_FILES_IN_FOLDER
            count_query = self.list_queries.COUNT_FILES_IN_FOLDER
        else:
            # List root files
            query = self.list_queries.LIST_ROOT_FILES
            count_query = self.list_queries.COUNT_ROOT_FILES

        # Set parameters
        params = {
            'user_id': user_id,
            'skip': skip,
            'limit': page_size
        }

        if folder_id:
            params['folder_id'] = folder_id

        # Execute count query
        count_result = execute_read_query(count_query, params)
        total_count = count_result[0]['total'] if count_result else 0

        # Execute main query
        result = execute_read_query(query, params)

        # Format results
        files = []
        for record in result:
            file_data = {
                'id': record['id'],
                'name': record['name'],
                'mime_type': record.get('mime_type', ''),
                'web_view_link': record.get('web_view_link', ''),
                'created_time': record['created_time'],
                'modified_time': record['modified_time'],
                'size': record.get('size', 0),
                'shared_with': record.get('shared_with', []),
                'is_folder': record['is_folder'],
                'parent_folder_id': folder_id if folder_id else '',
                'child_count': record['child_count']
            }
            files.append(file_data)

        return files, total_count, page, page_size

    def get_file_details(
        self, user_id: str, file_id: str) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Get details of a specific file or folder.

        Args:
            user_id: The ID of the user
            file_id: The ID of the file or folder

        Returns:
            Tuple containing:
            - success: Boolean indicating if operation was successful
            - message: Status message
            - file_data: The file data or None if not found
        """
        query = self.file_queries.GET_FILE_DETAILS

        params = {
            'user_id': user_id,
            'file_id': file_id
        }

        result = execute_read_query(query, params)

        if not result:
            return False, f"File with ID {file_id} not found", None

        record = result[0]
        file_data = {
            'id': record['id'],
            'name': record['name'],
            'mime_type': record.get('mime_type', ''),
            'web_view_link': record.get('web_view_link', ''),
            'created_time': record['created_time'],
            'modified_time': record['modified_time'],
            'size': record.get('size', 0),
            'shared_with': record.get('shared_with', []),
            'is_folder': record['is_folder'],
            'child_count': record['child_count']
        }

        return True, "File details retrieved successfully", file_data

    def _file_or_folder_exists_in_graph(self, item_id: str, organisation_id: str) -> bool:
        """
        Check if a file or folder already exists in the Neo4j graph.
        
        Args:
            item_id: The Google Drive file or folder ID
            organisation_id: The organization ID
            
        Returns:
            bool: True if the item exists, False otherwise
        """
        try:
            # Check for both files and folders with the given ID and organisation
            query = """
            MATCH (item)
            WHERE (item:GoogleDriveFile)
            AND item.id = $item_id
            AND item.organisation_id = $organisation_id
            RETURN COUNT(item) > 0 as exists
            """
            
            result = execute_read_query(query, {
                'item_id': item_id,
                'organisation_id': organisation_id
            })
            
            if result and len(result) > 0:
                return result[0].get('exists', False)
            return False
            
        except Exception as e:
            logger.error(f"Error checking if item exists in graph: {str(e)}")
            return False

    def check_file_access(self, user_id: str, file_id: str) -> bool:
        """
        Check if a user has access to a file or folder.
        Recursively checks parent folders if direct access is not found.

        Args:
            user_id: The ID of the user
            file_id: The ID of the file or folder

        Returns:
            Boolean indicating if the user has access
        """
        from app.utils.access_control import check_user_access
        return check_user_access(user_id, file_id)

    def get_folder_and_contents_by_id_service_account(
        self, organisation_id: str, folder_id: str) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Get a folder by ID and all its contents using service account.

        Args:
            organisation_id: The ID of the organisation
            folder_id: The ID of the folder to fetch

        Returns:
            Tuple containing:
            - success: Boolean indicating if operation was successful
            - message: Status message
            - folder_data: The folder data with contents or None if not found
        """
        try:
            # Get authenticated drive service with appropriate credentials
            drive_service = self._get_drive_service(organisation_id)
            if not drive_service:
                logger.error(
                    "Failed to get authenticated drive service for service account")
                return False, "Failed to authenticate with Google Drive", None

            # Get folder metadata
            folder = drive_service.files().get(
                fileId=folder_id,
                fields="id, name, mimeType, parents, createdTime, modifiedTime"
            ).execute()

            if not folder:
                return False, f"Folder with ID '{folder_id}' not found", None

            # Get folder contents
            query = f"'{folder_id}' in parents and trashed = false"
            fields = "files(id, name, mimeType, parents, createdTime, modifiedTime, size, webViewLink, permissions(emailAddress, role))"

            response = drive_service.files().list(
                q=query,
                spaces='drive',
                fields=fields,
                pageSize=100
            ).execute()

            items = response.get('files', [])

            # Format results
            children = []
            for item in items:
                is_folder = item['mimeType'] == 'application/vnd.google-apps.folder'
                child_count = 0

                if is_folder:
                    # Get child count for folders
                    child_query = f"'{item['id']}' in parents and trashed = false"
                    child_response = drive_service.files().list(
                        q=child_query,
                        spaces='drive',
                        fields="files(id)"
                    ).execute()
                    child_count = len(child_response.get('files', []))

                child_data = {
                    'id': item['id'],
                    'name': item['name'],
                    'mime_type': item.get('mimeType', ''),
                    'web_view_link': item.get('webViewLink', ''),
                    'created_time': item['createdTime'],
                    'modified_time': item['modifiedTime'],
                    'size': item.get('size', 0),
                    'shared_with': [perm.get('emailAddress') for perm in item.get('permissions', []) if 'emailAddress' in perm and perm.get('role') != 'owner'],
                    'is_folder': is_folder,
                    'parent_folder_id': folder_id,
                    'child_count': child_count
                }
                children.append(child_data)

            folder_data = {
                'id': folder_id,
                'name': folder['name'],
                'is_folder': True,
                'children': children,
                'child_count': len(children)
            }

            return True, "Folder and contents retrieved successfully", folder_data

        except Exception as e:
            logger.error(
                f"Error getting folder by ID with service account: {str(e)}")
            return False, f"Error: {str(e)}", None

    def _fetch_folder_contents_service_account(
        self, service, service_account_user_id: str, folder_id: str) -> Tuple[int, int]:
        """
        Fetch contents of a folder including all subfolders recursively using service account.

        Args:
            service: The Google Drive API service (authenticated with service account)
            service_account_user_id: Virtual user ID for the service account
            folder_id: The ID of the folder to fetch contents from

        Returns:
            Tuple containing:
            - files_synced: Number of files synced
            - folders_synced: Number of folders synced
        """
        files_synced = 0
        folders_synced = 0

        query = f"'{folder_id}' in parents and trashed = false"
        fields = "nextPageToken, files(id, name, mimeType, parents, createdTime, modifiedTime, size, webViewLink, permissions(emailAddress, role))"

        page_token = None
        while True:
            try:
                response = service.files().list(
                    q=query,
                    spaces='drive',
                    fields=fields,
                    pageToken=page_token,
                    pageSize=100
                ).execute()

                items = response.get('files', [])

                for item in items:
                    is_folder = item['mimeType'] == 'application/vnd.google-apps.folder'

                    if is_folder:
                        self._create_or_update_folder(
                            service_account_user_id, item)
                        folders_synced += 1

                        # Recursive call to fetch subfolders
                        sub_files, sub_folders = self._fetch_folder_contents_service_account(
                            service,
                            service_account_user_id,
                            item['id']
                        )
                        files_synced += sub_files
                        folders_synced += sub_folders
                    else:
                        self._create_or_update_file(
    service_account_user_id, item, service)
                        files_synced += 1

                page_token = response.get('nextPageToken')
                if not page_token:
                    break

            except Exception as e:
                logger.error(
                    f"Error processing folder contents with service account: {str(e)}")
                break

        return files_synced, folders_synced

    def search_similar_documents(self,
    user_id: str,
    query_text: str,
    top_k: int = 5,
    agent_id: Optional[str] = None,
    organisation_id: str = None,
    file_ids: Optional[List[str]] = None,
    least_score: Optional[float] = None) -> Tuple[bool,
    str,
    List[Dict[str,
     Any]]]:
        """
        Search for documents semantically similar to the query text.

        Args:
            user_id: The ID of the user
            query_text: The query text to search for
            top_k: The number of results to return
            agent_id: Optional ID of the agent. If provided, only return files accessible to both user and agent's department
            organisation_id: ID of the organization (mandatory but not used currently)
            file_ids: Optional list of specific file IDs to search within. If provided, search is limited to these files only.
            least_score: Optional minimum score threshold. If provided, only return results with score >= least_score.

        Returns:
            Tuple containing:
            - success: Boolean indicating if search was successful
            - message: Status message
            - results: List of search results
        """
        if not self.pinecone_service.is_initialized():
            return False, "Pinecone service not initialized", []

        try:
            # Initialize hybrid search engine with pinecone service
            hybrid_engine = HybridSearchEngine(pinecone_service=self.pinecone_service)
            search_file_ids = self.pinecone_service.get_search_file_ids(user_id, agent_id, organisation_id, file_ids)
            if file_ids:
                search_file_ids = file_ids & search_file_ids
            print("SEARCH FILE IDS: ", search_file_ids)
            if not search_file_ids:
                return True, "User does not have access to any uploaded document", []
            # Perform hybrid search
            hybrid_response = hybrid_engine.search_with_query(
                query_text=query_text,
                user_id=user_id,
                top_k=top_k,
                agent_id=agent_id,
                organisation_id=organisation_id,
                file_ids=search_file_ids
            )
            # Apply least_score filter if provided
            if least_score is not None:
                filtered_results = []
                for result in hybrid_response.results:
                    score = result.get('score', 0.0)
                    if score >= least_score:
                        filtered_results.append(result)
                hybrid_response.results = filtered_results
            
            # Return the HybridResponse object directly so gRPC service can access graph_context
            return hybrid_response.success, hybrid_response.message, hybrid_response
            
        except Exception as e:
            logger.error(f"Error in hybrid search: {str(e)}")
            return False, f"Error in hybrid search: {str(e)}", []

    def batch_search_similar_documents(self,
    user_id: str,
    query_texts: List[str],
    top_k: int = 5,
    agent_id: Optional[str] = None,
    organisation_id: str = None,
    file_ids: Optional[List[str]] = None,
    least_score: Optional[float] = None) -> Tuple[bool,
    str,
    List[List[Dict[str,
     Any]]]]:
        """
        Perform batch search for documents semantically similar to multiple query texts.
        This is more efficient than calling search_similar_documents multiple times.

        Args:
            user_id: The ID of the user
            query_texts: List of query texts to search for
            top_k: The number of results to return for each query
            agent_id: Optional ID of the agent. If provided, only return files accessible to both user and agent's department
            organisation_id: ID of the organization (mandatory but not used currently)
            file_ids: Optional list of specific file IDs to search within. If provided, search is limited to these files only.
            least_score: Optional minimum score threshold. If provided, only return results with score >= least_score.

        Returns:
            Tuple containing:
            - success: Boolean indicating if search was successful
            - message: Status message
            - results: List of search results for each query, where each item is a list of results
        """
        if not self.pinecone_service.is_initialized():
            return False, "Pinecone service not initialized", []

        try:
            # Initialize hybrid search engine with pinecone service
            hybrid_engine = HybridSearchEngine(pinecone_service=self.pinecone_service)
            search_file_ids = self.pinecone_service.get_search_file_ids(user_id, agent_id, organisation_id, file_ids)
            if file_ids:
                search_file_ids = file_ids & search_file_ids
            
            # Perform hybrid search for each query
            all_results = []
            for query_text in query_texts:
                # Perform hybrid search for this query
                hybrid_response = hybrid_engine.search_with_query(
                    query_text=query_text,
                    user_id=user_id,
                    top_k=top_k,
                    agent_id=agent_id,
                    organisation_id=organisation_id,
                    file_ids=search_file_ids
                )
                
                # Convert HybridSearchResponse to legacy format for this query
                query_results = []
                for result in hybrid_response.results:
                    score = result.get('score', 0.0)
                    
                    # Apply least_score filter if provided
                    if least_score is not None and score < least_score:
                        continue
                    
                    entities = result.get('entities', [])
                    relationships = result.get('relationships', [])
                    
                    query_results.append({
                        'file_id': result.get('file_id', ''),
                        'file_name': result.get('file_name', ''),
                        'mime_type': result.get('mime_type', ''),
                        'web_view_link': result.get('web_view_link', ''),
                        'created_time': result.get('created_time', ''),
                        'modified_time': result.get('modified_time', ''),
                        'score': score,
                        'vector_id': result.get('vector_id', ''),
                        'chunk_text': result.get('chunk_text', ''),
                        'search_type': result.get('search_type', 'hybrid'),
                        # Chunk-specific entities and relationships
                        'entities': entities,
                        'relationships': relationships,
                        # Additional metadata
                        'entity_count': len(entities),
                        'relationship_count': len(relationships),
                        'has_graph_context': len(entities) > 0 or len(relationships) > 0,
                        'entity_types': list(set(e.get('type', '') for e in entities if e.get('type'))),
                        'relationship_types': list(set(r.get('type', '') for r in relationships if r.get('type')))
                    })
                
                all_results.append(query_results)
            
            return True, f"Found results for {len(query_texts)} queries using hybrid search", all_results
            
        except Exception as e:
            logger.error(f"Error in batch hybrid search: {str(e)}")
            return False, f"Error in batch hybrid search: {str(e)}", []


    def validate_service_account_source(
        self, organisation_id: str) -> Tuple[bool, str, List[Dict[str, Any]]]:
        """
        Validate service account for an organisation and return accessible folders.

        Args:
            organisation_id: Organisation ID

        Returns:
            Tuple containing:
            - success: Boolean indicating validation success
            - message: Status message
            - folders: List of accessible folders
        """
        try:
            # Get service account credentials
            success, credentials_or_error = get_service_account_credentials(
                organisation_id)
            if not success:
                return False, credentials_or_error, []

            # Validate service account access
            return self.service_account_manager.validate_service_account_access(
                credentials_or_error)

        except Exception as e:
            logger.error(f"Error validating service account source: {str(e)}")
            return False, f"Validation error: {str(e)}", []

    def get_service_account_drive_service(self, organisation_id: str):
        """
        Get authenticated Google Drive service using organisation's service account.

        Args:
            organisation_id: Organisation ID

        Returns:
            Authenticated Google Drive service or None if failed
        """
        try:
            success, credentials_json = get_service_account_credentials_legacy(
                organisation_id)
            if not success:
                logger.error(
                    f"Failed to get service account credentials: {credentials_json}")
                return None

            return self.service_account_manager.get_authenticated_drive_service(
                credentials_json)

        except Exception as e:
            logger.error(
                f"Error creating service account drive service: {str(e)}")
            return None

    def get_service_account_credentials_json(
        self, organisation_id: str) -> str:
        """
        Get service account credentials JSON for an organisation.

        Args:
            organisation_id: Organisation ID

        Returns:
            Service account credentials JSON string or empty string if not found
        """
        try:
            success, credentials_json = get_service_account_credentials_legacy(
                organisation_id)
            if not success:
                logger.error(
                    f"Failed to get service account credentials: {credentials_json}")
                return ""

            return credentials_json

        except Exception as e:
            logger.error(
                f"Error getting service account credentials JSON: {str(e)}")
            return ""

    def create_folder_nodes_with_permissions(self,
    organisation_id: str,
    folders: List[Dict[str,
    Any]],
    file_ids: Optional[List[str]] = None) -> Tuple[bool,
    List[Dict[str,
     str]]]:
        """
        Create folder nodes with permission-based access control.

        Args:
            organisation_id: Organisation ID
            folders: List of folder data from Google Drive with permissions
            file_ids: Optional list of specific file IDs to sync. If provided, only these files will be synced.

        Returns:
            Tuple containing:
            - Boolean indicating success
            - List of synced files with their IDs and names
        """
        try:
            synced_files = []

            for folder in folders:
                # Extract permission emails for Neo4j query
                permissions = folder.get('permissions', [])
                permission_emails = []
                for perm in permissions:
                    if 'emailAddress' in perm and perm.get('type') == 'user':
                        permission_emails.append(perm['emailAddress'])

                # Create folder node with permissions data
                folder_params = {
                    'folder_id': folder['id'],
                    'name': folder['name'],
                    'organisation_id': organisation_id,
                    'created_time': folder.get('created_time'),
                    'modified_time': folder.get('modified_time'),
                    'owners': json.dumps(folder.get('owners', [])),
                    'permissions_count': folder.get('permissions_count', 0),
                    'permissions': json.dumps(folder.get('permissions', [])),
                    'permission_emails': permission_emails,
                    'is_top_level': True,
                    'updated_at': datetime.utcnow().isoformat()
                }

                execute_write_query(
    self.folder_queries.CREATE_OR_UPDATE_FOLDER_WITH_PERMISSIONS,
     folder_params)

                # Process permissions for this folder to create user nodes and relationships
                self._process_folder_permissions(folder, organisation_id)

            # Process files if provided
            if file_ids and len(file_ids) > 0:
                synced_files = self._sync_specific_files_with_permissions(
                    organisation_id, file_ids)

            return True, synced_files

        except Exception as e:
            logger.error(
                f"Error creating folder nodes with permissions: {str(e)}")
            return False, []

    def _process_folder_permissions(
        self, folder_data: Dict[str, Any], organisation_id: str):
        """
        Process Google Drive folder permissions and create user access.
        """
        permissions = folder_data.get('permissions', [])

        for permission in permissions:
            email = permission.get('emailAddress')
            role = permission.get('role')  # owner, editor, viewer
            perm_type = permission.get('type')  # user, group, domain, anyone

            if email and role and perm_type == 'user':
                # Skip service account emails (they end with .iam.gserviceaccount.com)
                if email.endswith('.iam.gserviceaccount.com'):
                    continue
                
                # Create or find user (only for real user-type permissions, not service accounts)
                self._create_or_find_user_by_email(email, organisation_id)

                # Create folder access
                self._create_user_folder_access(email, folder_data['id'], role, organisation_id)

    def _create_or_find_user_by_email(
    self, email: str, organisation_id: str) -> str:
        """
        Create or find user by email address.
        """
        user_id = str(uuid.uuid4())
        current_time = datetime.utcnow().isoformat()

        # Extract name from email (simple approach)
        name = email.split('@')[0].replace('.', ' ').title()

        params = {
            'email': email,
            'user_id': user_id,
            'organisation_id': organisation_id,
            'name': name,
            'created_at': current_time,
            'updated_at': current_time
        }

        result = execute_write_query(
    self.user_queries.CREATE_OR_FIND_USER_BY_EMAIL, params)
        return result[0]['u']['id'] if result else user_id

    def _create_user_folder_access(
    self,
    email: str,
    folder_id: str,
     role: str,
     organisation_id: str):
        """
        Create user access to folder based on Google Drive permissions.
        """
        params = {
            'email': email,
            'folder_id': folder_id,
            'role': role,
            'organisation_id': organisation_id,
            'granted_at': datetime.utcnow().isoformat()
        }

        execute_write_query(
    self.user_queries.CREATE_USER_FOLDER_ACCESS, params)

    def _sync_specific_files_with_permissions(
        self, organisation_id: str, file_ids: List[str]) -> List[Dict[str, str]]:
        """
        Sync specific files with permission-based access.
        """
        synced_files = []
        drive_service = self._get_drive_service(organisation_id)

        if not drive_service:
            return synced_files

        for file_id in file_ids:
            try:
                # Get file with permissions
                file_data = drive_service.files().get(
                    fileId=file_id,
                    fields="id, name, mimeType, parents, createdTime, modifiedTime, size, webViewLink, permissions(emailAddress, role)"
                ).execute()

                # Create file node
                self._create_file_with_permissions(file_data, organisation_id)

                # Process file permissions
                self._process_file_permissions(file_data, organisation_id)

                synced_files.append({
                    'id': file_data['id'],
                    'name': file_data['name']
                })

            except Exception as e:
                logger.error(f"Error syncing file {file_id}: {str(e)}")

        return synced_files

    def _create_file_with_permissions(
        self, file_data: Dict[str, Any], organisation_id: str):
        """
        Create file node with permissions data.
        """
        # Create a virtual user ID for the service account
        service_account_user_id = f"sa_{organisation_id}"

        # Create or update the file in Neo4j
        self._create_or_update_file(
    service_account_user_id,
    file_data,
    None,
     organisation_id)

    def _process_file_permissions(
        self, file_data: Dict[str, Any], organisation_id: str):
        """
        Process Google Drive file permissions and create user access.
        """
        permissions = file_data.get('permissions', [])

        for permission in permissions:
            email = permission.get('emailAddress')
            role = permission.get('role')
            perm_type = permission.get('type')  # user, group, domain, anyone

            if email and role and perm_type == 'user':
                # Skip service account emails (they end with .iam.gserviceaccount.com)
                if email.endswith('.iam.gserviceaccount.com'):
                    continue
                
                # Create or find user (only for real user-type permissions, not service accounts)
                self._create_or_find_user_by_email(email, organisation_id)

                # Create file access
                self._create_user_file_access(email, file_data['id'], role, organisation_id)

    def _create_user_file_access(self, email: str, file_id: str, role: str, organisation_id: str):
        """
        Create user access to file based on Google Drive permissions.
        """
        params = {
            'email': email,
            'file_id': file_id,
            'role': role,
            'organisation_id': organisation_id,
            'granted_at': datetime.utcnow().isoformat()
        }

        execute_write_query(self.user_queries.CREATE_USER_FILE_ACCESS, params)

    def create_folder_nodes_from_service_account(self,
    organisation_id: str,
    folders: List[Dict[str,
    Any]],
    file_ids: Optional[List[str]] = None) -> Tuple[bool,
    List[Dict[str,
     str]]]:
        """
        Create folder nodes in Neo4j from service account accessible folders.

        Args:
            organisation_id: Organisation ID
            folders: List of folder data from Google Drive
            file_ids: Optional list of specific file IDs to sync. If provided, only these files will be synced.

        Returns:
            Tuple containing:
            - Boolean indicating success
            - List of synced files with their IDs and names
        """
        try:
            # Initialize list to track synced files
            synced_files = []

            for folder in folders:
                # Create folder node with GoogleDriveFolder label to ensure
                # proper integration
                params = {
                    'folder_id': folder['id'],
                    'name': folder['name'],
                    'organisation_id': organisation_id,
                    'created_time': folder.get('created_time'),
                    'modified_time': folder.get('modified_time'),
                    'owners': folder.get('owners', []),
                    'permissions_count': folder.get('permissions_count', 0),
                    'updated_at': datetime.utcnow().isoformat()
                }

                execute_write_query(
    self.folder_queries.CREATE_OR_UPDATE_FOLDER, params)

            # Create relationships between departments and folders for access
            # control
            execute_write_query(
    self.service_account_queries.CREATE_DEPARTMENT_FOLDER_ACCESS, {
        'organisation_id': organisation_id})

            # If specific file_ids are provided, sync only those files
            if file_ids and len(file_ids) > 0:
                logger.info(
                    f"Syncing {len(file_ids)} specific files for organisation {organisation_id}")

                # Get authenticated drive service with appropriate credentials
                drive_service = self._get_drive_service(organisation_id)
                if not drive_service:
                    logger.error(
                        "Failed to get authenticated drive service for service account")
                    return False, []

                # Create a virtual user ID for the service account
                service_account_user_id = f"sa_{organisation_id}"

                # Fetch and sync each specified file
                for file_id in file_ids:
                    try:
                        # Get file metadata
                        file_data = drive_service.files().get(
                            fileId=file_id,
                            fields="id, name, mimeType, parents, createdTime, modifiedTime, size, webViewLink, permissions(emailAddress, role)"
                        ).execute()

                        # Create or update the file in Neo4j
                        self._create_or_update_file(
    service_account_user_id, file_data, drive_service, organisation_id)
                        logger.info(
                            f"Synced file {file_data['name']} (ID: {file_id})")

                        # Add to synced files list
                        synced_files.append({
                            'id': file_id,
                            'name': file_data['name']
                        })
                    except Exception as file_error:
                        logger.error(
                            f"Error syncing file {file_id}: {str(file_error)}")

                # Create folder relationships to ensure proper hierarchy
                self._create_folder_relationships(
    service_account_user_id, organisation_id)

            return True, synced_files

        except Exception as e:
            logger.error(f"Error creating folder nodes: {str(e)}")
            return False, []

    def map_user_folder_access(self, organisation_id: str) -> bool:
        """
        Map user access to folders based on department memberships.

        Args:
            organisation_id: Organisation ID

        Returns:
            Boolean indicating success
        """
        try:
            # Create relationships: Users in organisation get access to all folders
            # This is a simplified approach - in reality, you'd want more granular permissions
            # First, ensure all users have access to top-level folders
            execute_write_query(
    self.service_account_queries.MAP_USER_FOLDER_ACCESS, {
        'organisation_id': organisation_id})

            # Ensure the GENERAL department has access to all top-level folders
            execute_write_query(
    self.service_account_queries.MAP_GENERAL_DEPARTMENT_ACCESS, {
        'organisation_id': organisation_id})

            logger.info(
                f"Successfully mapped user and department access to top-level folders for organisation {organisation_id}")
            return True

        except Exception as e:
            logger.error(f"Error mapping user folder access: {str(e)}")
            return False
# First connection established -> immediately list all folders -> return to frontend
# Frontend will send folderid + deparmnent id -> tag/map them


    def _sync_files_and_folders_service_account(
    self, service, organisation_id: str, full_sync: bool = False) -> Tuple[int, int]:
        """
        Sync all files and folders from Google Drive using service account.

        Args:
            service: The Google Drive service instance
            organisation_id: The ID of the organization
            full_sync: Whether to perform a full sync

        Returns:
            Tuple containing:
            - files_synced: Number of files synced
            - folders_synced: Number of folders synced
        """
        files_synced = 0
        folders_synced = 0

        try:
            # Build query for files and folders
            query = "trashed = false"
            if not full_sync:
                # For incremental sync, get files modified in last 24 hours
                from datetime import datetime, timedelta
                yesterday = (datetime.utcnow() -
                             timedelta(days=1)).isoformat() + 'Z'
                query += f" and modifiedTime > '{yesterday}'"

            fields = "nextPageToken, files(id, name, mimeType, parents, createdTime, modifiedTime, size, webViewLink, permissions)"
            page_token = None
            i = 0
            while True:
                logger.info(f"Iteration {i}")
                i += 1
                response = service.files().list(
                    q=query,
                    spaces='drive',
                    fields=fields,
                    pageToken=page_token,
                    pageSize=100
                ).execute()

                items = response.get('files', [])

                for item in items:
                    # Check if the file/folder already exists in the graph
                    if self._file_or_folder_exists_in_graph(item['id'], organisation_id):
                        logger.debug(f"Skipping {item['name']} (ID: {item['id']}) - already exists in graph")
                        continue
                    
                    if item.get(
                        'mimeType') == 'application/vnd.google-apps.folder':
                        # Process folder with permissions
                        self._create_or_update_folder_with_permissions(
                            item, organisation_id)
                        self._create_folder_folder_relationships_immediate(item, organisation_id)
                        folders_synced += 1
                    else:
                        # Process file with permissions
                        self._create_or_update_file_with_permissions(
                            item, organisation_id)
                        self._create_file_folder_relationships_immediate(
                            item, organisation_id
                        )
                        files_synced += 1

                page_token = response.get('nextPageToken')
                if not page_token:
                    break

        except Exception as e:
            logger.error(f"Error syncing files and folders: {str(e)}")

        return files_synced, folders_synced

    def _create_or_update_folder_with_permissions(self, folder_data: Dict[str, Any], organisation_id: str) -> None:
        """
        Create or update a folder with permission-based access control.

        Args:
            folder_data: The folder data from Google Drive API
            organisation_id: The ID of the organization
        """
        try:
            # Extract permissions and create user nodes for people with access
            permissions = folder_data.get('permissions', [])
            user_emails = []

            for permission in permissions:
                if permission.get('type') == 'user' and permission.get('emailAddress'):
                    email = permission['emailAddress']
                    # Exclude service account emails (they typically end with .iam.gserviceaccount.com)
                    if not email.endswith('.iam.gserviceaccount.com'):
                        user_emails.append(email)

            # Create or update folder using repository
            query = self.folder_queries.CREATE_OR_UPDATE_FOLDER_WITH_PERMISSIONS
            
            # Convert permissions to JSON string for storage
            permissions_json = json.dumps(permissions) if permissions else "[]"
            owners_json = json.dumps(folder_data.get('owners', []))
            
            params = {
                'folder_id': folder_data['id'],
                'name': folder_data['name'],
                'organisation_id': organisation_id,
                'created_time': folder_data.get('createdTime', ''),
                'modified_time': folder_data.get('modifiedTime', ''),
                'permissions': permissions_json,
                'owners': owners_json,
                'permissions_count': len(permissions),
                'is_top_level': 'root' in folder_data.get('parents', []),
                'parents': folder_data.get('parents', []),
                'updated_at': datetime.utcnow().isoformat(),
                'permission_emails': user_emails
            }

            execute_write_query(query, params)

        except Exception as e:
            logger.error(f"Error creating/updating folder with permissions: {str(e)}")

    def _create_or_update_file_with_permissions(self, file_data: Dict[str, Any], organisation_id: str) -> None:
        """
        Create or update a file with permission-based access control.

        Args:
            file_data: The file data from Google Drive API
            organisation_id: The ID of the organization
        """
        try:
            logger.info(f"Processing file: {file_data['name']} (ID: {file_data['id']})")  
            # Extract permissions and create user nodes for people with access
            permissions = file_data.get('permissions', [])
            user_emails = []

            for permission in permissions:
                if permission.get('type') == 'user' and permission.get('emailAddress'):
                    email = permission['emailAddress']
                    # Exclude service account emails (they typically end with .iam.gserviceaccount.com)
                    if not email.endswith('.iam.gserviceaccount.com'):
                        user_emails.append(email)

            # Convert permissions to JSON string for storage (consistent with folder handling)
            permissions_json = json.dumps(permissions) if permissions else "[]"

            # Create or update file using repository
            query = self.file_queries.CREATE_OR_UPDATE_FILE_WITH_PERMISSIONS
            params = {
                'file_id': file_data['id'],
                'name': file_data['name'],
                'organisation_id': organisation_id,
                'mime_type': file_data.get('mimeType', ''),
                'size': int(file_data.get('size', 0)),
                'created_time': file_data.get('createdTime', ''),
                'modified_time': file_data.get('modifiedTime', ''),
                'web_view_link': file_data.get('webViewLink', ''),
                'vector_id': None,
                'content_hash': None,
                'parents': file_data.get('parents',[]),
                'permissions': permissions_json,  # Fixed: Now passing JSON string instead of complex object
                'shared_with': user_emails,
                'permission_emails': user_emails
            }

            execute_write_query(query, params)

            # Process file for Pinecone if Pinecone is initialized
            if self.pinecone_service.is_initialized():
                try:
                    # Check if file is already vectorized and get last modified time
                    check_result = execute_read_query(
                        self.metadata_queries.CHECK_FILE_VECTORIZATION_STATUS, {
                            'file_id': file_data['id']
                        })

                    # Get current file's modified time
                    current_modified_time = file_data['modifiedTime']

                    # Check if file needs to be vectorized:
                    # 1. If it has never been vectorized (no vector_id)
                    # 2. If the file has been modified since last vectorization
                    needs_vectorization = True

                    if check_result and check_result[0].get('f.vector_id'):
                        last_vectorized_modified_time = check_result[0].get(
                            'f.last_vectorized_modified_time')

                        if last_vectorized_modified_time and last_vectorized_modified_time == current_modified_time:
                            # File hasn't changed since last vectorization
                            needs_vectorization = False
                            logger.info(
                                f"File {file_data['name']} hasn't changed since last vectorization, skipping")

                    if needs_vectorization:
                        # Get drive service with appropriate credentials (OAuth or service account)
                        service = self._get_drive_service(organisation_id)
                        if service:
                            # Extract text from file
                            text = self.pinecone_service.extract_text_from_file(
                                file_data, service)

                            if text:
                                # If file was previously vectorized, we need to update the existing vector
                                existing_vector_id = check_result[0].get(
                                    'f.vector_id') if check_result else None

                                # Extract user ID from file permissions instead of using virtual service account user ID
                                effective_user_id = self._extract_user_id_from_permissions(file_data, organisation_id)
                                
                                # Upload to Pinecone with entity extraction (update if vector_id exists)
                                success, message, vector_id = self.pinecone_service.enhanced_upload_file_to_pinecone(
                                    effective_user_id, organisation_id, file_data, text, existing_vector_id
                                )

                                if success and vector_id:
                                    # Update Neo4j with vector ID using repository
                                    update_query = self.file_queries.UPDATE_FILE_VECTOR_ID
                                    update_params = {
                                        'file_id': file_data['id'],
                                        'vector_id': vector_id
                                    }
                                    execute_write_query(update_query, update_params)

                                    # Update additional vectorization metadata
                                    metadata_params = {
                                        'file_id': file_data['id'],
                                        'vectorized_at': datetime.utcnow().isoformat(),
                                        'modified_time': current_modified_time
                                    }

                                    execute_write_query(
                                        self.metadata_queries.UPDATE_FILE_VECTORIZATION_METADATA, metadata_params)

                                    if existing_vector_id:
                                        logger.info(
                                            f"File {file_data['name']} updated in Pinecone with new content")
                                    else:
                                        logger.info(
                                            f"File {file_data['name']} vectorized and stored in Pinecone")
                                else:
                                    logger.warning(
                                        f"Failed to vectorize file {file_data['name']}: {message}")
                            else:
                                logger.info(
                                    f"No text extracted from file {file_data['name']}")

                except Exception as e:
                    logger.error(f"Error processing file for Pinecone: {str(e)}")

        except Exception as e:
            logger.error(f"Error creating/updating file with permissions: {str(e)}")

    def _cancel_scheduled_syncs_for_organization(self, organisation_id: str) -> None:
        """
        Cancel all scheduled syncs for an organization.

        Args:
            organisation_id: The ID of the organization
        """
        try:
            # Find all sync jobs for this organization
            pattern = f"gdrive_sync:*:{organisation_id}:*"
            keys = self.redis_service.keys(pattern)
            
            # Remove each job from the queue and delete the job data
            for key in keys:
                self.redis_service.zrem("gdrive_sync_queue", key)
                self.redis_service.delete(key)
                
        except Exception as e:
            logger.error(f"Error canceling scheduled syncs: {str(e)}")

    def _create_file_folder_relationships_immediate(self, file_data, organisation_id):
        """
        Create folder-file relationships immediately after file creation.
        This ensures relationships are created even if batch processing fails.
        
        Args:
            file_data: File data from Google Drive API
            organisation_id: The organisation ID
        """
        try:
            parent_ids = file_data.get('parents', [])
            if not parent_ids:
                return
            
            file_id = file_data['id']
            
            # Create relationship for each parent folder
            for parent_id in parent_ids:
                try:
                    relationship_params = {
                        'file_id': file_id,
                        'folder_id': parent_id,
                        'organisation_id': organisation_id
                    }
                    
                    # Use the individual relationship query
                    execute_write_query(
                        self.sync_queries.CREATE_FILE_FOLDER_RELATIONSHIP,
                        relationship_params
                    )
                    
                    logger.debug(f"Created relationship: folder {parent_id} -> file {file_id}")
                    
                except Exception as rel_error:
                    logger.warning(f"Failed to create relationship for file {file_id} -> folder {parent_id}: {str(rel_error)}")
                    # Continue with other relationships even if one fails
                    
        except Exception as e:
            logger.error(f"Error creating immediate file-folder relationships for {file_data.get('name', 'unknown')}: {str(e)}")

    def _create_folder_folder_relationships_immediate(self, folder_data, organisation_id):
        """
        Create folder-folder relationships immediately after folder creation.
        This ensures parent-child folder relationships are created even if batch processing fails.
        
        Args:
            folder_data: Folder data from Google Drive API
            organisation_id: The organisation ID
        """
        try:
            parent_ids = folder_data.get('parents', [])
            if not parent_ids:
                return
            
            folder_id = folder_data['id']
            
            # Create relationship for each parent folder
            for parent_id in parent_ids:
                try:
                    relationship_params = {
                        'child_id': folder_id,
                        'parent_id': parent_id,
                        'organisation_id': organisation_id
                    }
                    
                    # Use the individual folder relationship query
                    execute_write_query(
                        self.sync_queries.CREATE_FOLDER_HIERARCHY_RELATIONSHIP,
                        relationship_params
                    )
                    
                    logger.debug(f"Created folder relationship: parent {parent_id} -> child {folder_id}")
                    
                except Exception as rel_error:
                    logger.warning(f"Failed to create folder relationship for {folder_id} -> parent {parent_id}: {str(rel_error)}")
                    # Continue with other relationships even if one fails
                    
        except Exception as e:
            logger.error(f"Error creating immediate folder-folder relationships for {folder_data.get('name', 'unknown')}: {str(e)}")
                    # Continue with other relationships even if one fails
                    
        except Exception as e:
            logger.error(f"Error creating immediate file-folder relationships for {folder_data.get('name', 'unknown')}: {str(e)}")
            # Remove from sorted set and delete keys
            for key in keys:
                self.redis_service.zrem("gdrive_sync_queue", key)
                self.redis_service.delete(key)

        except Exception as e:
            logger.error(f"Error canceling scheduled syncs: {str(e)}")
    def _create_or_update_generic_file(self, user_id: str, file_data: Dict[str, Any], organisation_id: str, agent_id: str) -> None:
        """
        Create or update a generic file in Neo4j and Pinecone with chunking and knowledge graph extraction.

        Args:
            user_id: The ID of the user
            file_data: The file data from generic file processing
            organisation_id: The ID of the organisation that owns this file
        """
        # Create or update file node using repository
        query = self.file_queries.CREATE_OR_UPDATE_FILE
        params = {
            'user_id': user_id,
            'organisation_id': organisation_id,
            'agent_id': agent_id,
            'file_id': file_data['id'],
            'name': file_data['name'],
            'mime_type': file_data['mime_type'],
            'size': file_data.get('size', 0),
            'web_view_link': file_data.get('web_view_link', ''),
            'created_time': file_data.get('created_time', datetime.utcnow().isoformat()),
            'modified_time': file_data.get('modified_time', datetime.utcnow().isoformat()),
            'vector_id': None,  # Will be updated later if vectorized
            'content_hash': file_data.get('content_hash'),
            'permissions': json.dumps([])  # Generic files don't have Google Drive permissions
        }

        execute_write_query(query, params)

        # Store additional properties for generic files
        additional_params = {
            'file_id': file_data['id'],
            'parent_ids': [],  # Generic files don't have parent folders
            'shared_with': [],  # Generic files don't have sharing info
            'current_time': datetime.utcnow().isoformat(),
            'organisation_id': organisation_id
        }

        execute_write_query(
            self.metadata_queries.UPDATE_FILE_ADDITIONAL_PROPERTIES,
            additional_params
        )

        # Invalidate the access cache for this user since file access has been updated
        self.pinecone_service.invalidate_access_cache(user_id)

        # Process file for Pinecone with chunking and knowledge graph extraction if Pinecone is initialized
        if self.pinecone_service.is_initialized():
            try:
                # Check if file is already vectorized and get last modified time
                check_result = execute_read_query(
                    self.metadata_queries.CHECK_FILE_VECTORIZATION_STATUS, {
                        'file_id': file_data['id']
                    }
                )

                # Get current file's modified time
                current_modified_time = file_data.get('modified_time', datetime.utcnow().isoformat())

                # Check if file needs to be vectorized:
                # 1. If it has never been vectorized (no vector_id)
                # 2. If the file has been modified since last vectorization
                needs_vectorization = True

                if check_result and check_result[0].get('f.vector_id'):
                    last_vectorized_modified_time = check_result[0].get('f.last_vectorized_modified_time')

                    if last_vectorized_modified_time and last_vectorized_modified_time == current_modified_time:
                        # File hasn't changed since last vectorization
                        needs_vectorization = False
                        logger.info(f"File {file_data['name']} hasn't changed since last vectorization, skipping")

                if needs_vectorization:
                    # Get text content from file_data (should be provided by GenericFileService)
                    text = file_data.get('content', '')

                    if text:
                        # If file was previously vectorized, we need to update the existing vector
                        existing_vector_id = check_result[0].get('f.vector_id') if check_result else None

                        # Upload to Pinecone with entity extraction (update if vector_id exists)
                        # Use the same enhanced_upload_file_to_pinecone method as sync drive
                        success, message, vector_id = self.pinecone_service.enhanced_upload_file_to_pinecone(
                            user_id, organisation_id, file_data, text, existing_vector_id
                        )

                        if success and vector_id:
                            # Update Neo4j with vector ID using repository
                            update_query = self.file_queries.UPDATE_FILE_VECTOR_ID
                            update_params = {
                                'file_id': file_data['id'],
                                'vector_id': vector_id
                            }
                            execute_write_query(update_query, update_params)

                            # Update additional vectorization metadata
                            metadata_params = {
                                'file_id': file_data['id'],
                                'vectorized_at': datetime.utcnow().isoformat(),
                                'modified_time': current_modified_time
                            }

                            execute_write_query(
                                self.metadata_queries.UPDATE_FILE_VECTORIZATION_METADATA, metadata_params
                            )

                            if existing_vector_id:
                                logger.info(f"File {file_data['name']} updated in Pinecone with new content and knowledge graph extraction")
                            else:
                                logger.info(f"File {file_data['name']} vectorized and stored in Pinecone with knowledge graph extraction")
                        else:
                            logger.warning(f"Failed to vectorize file {file_data['name']}: {message}")
                    else:
                        logger.info(f"No text content available for file {file_data['name']}")

            except Exception as e:
                logger.error(f"Error processing generic file for Pinecone: {str(e)}")

    def _create_file_relationships(self, file_id: str, agent_id: str, user_id: Optional[str], organisation_id: str) -> None:
        """
        Create mandatory relationships for a file.

        Args:
            file_id: The ID of the file
            agent_id: The ID of the agent
            user_id: Optional ID of the user
            organisation_id: The ID of the organisation
        """

        #print("file_id, agent_id, user_id, organisation_id ", file_id, agent_id, user_id, organisation_id)
        # Create relationship between agent and file
        execute_write_query(self.relationship_queries.CREATE_AGENT_FILE_RELATIONSHIP, {
            'agent_id': agent_id,
            'file_id': file_id
        })

        # If user_id is provided, create relationship between user and file
        if user_id and not user_id.startswith('sa_'):  # Don't create user relationships for service accounts
            execute_write_query(self.relationship_queries.CREATE_USER_FILE_RELATIONSHIP, {
                'user_id': user_id,
                'file_id': file_id
            })

        # Create department-file relationship based on agent's department
        execute_write_query(self.relationship_queries.CREATE_DEPARTMENT_FILE_RELATIONSHIP, {
            'agent_id': agent_id,
            'file_id': file_id
        })

    def sync_folder_recursively_with_permissions(self, service, organisation_id: str, folder_id: str) -> Tuple[int, int]:
        """
        Sync a specific folder and all its contents recursively using service account.
        Uses existing functions for creating files in Neo4j and Pinecone.
        Extracts user ID from file permissions instead of creating virtual service account user ID.

        Args:
            service: The Google Drive API service (authenticated with service account)
            organisation_id: The ID of the organisation
            folder_id: The ID of the folder to sync recursively

        Returns:
            Tuple containing:
            - files_synced: Number of files synced
            - folders_synced: Number of folders synced
        """
        files_synced = 0
        folders_synced = 0

        try:
            # Get folder metadata with permissions
            folder_data = service.files().get(
                fileId=folder_id,
                fields="id, name, mimeType, parents, createdTime, modifiedTime, permissions(emailAddress, role, type)"
            ).execute()

            # Create or update the folder using existing permissions-based function
            self._create_or_update_folder_with_permissions(folder_data, organisation_id)
            folders_synced += 1

            # Process folder permissions to create users and user-folder relationships
            self._process_folder_permissions(folder_data, organisation_id)

            # Now fetch all contents of this folder recursively
            sub_files, sub_folders = self._fetch_folder_contents_recursively_with_permissions(
                service,
                organisation_id,
                folder_id
            )
            files_synced += sub_files
            folders_synced += sub_folders

            # Create folder hierarchy relationships using existing function
            self._create_folder_folder_relationships_immediate(folder_data, organisation_id)

            return files_synced, folders_synced

        except Exception as e:
            logger.error(f"Error syncing folder recursively with permissions: {str(e)}")
            return 0, 0

    def _fetch_folder_contents_recursively_with_permissions(self, service, organisation_id: str, folder_id: str) -> Tuple[int, int]:
        """
        Fetch contents of a folder including all subfolders recursively using service account.
        Uses existing functions that handle Neo4j and Pinecone with permissions.

        Args:
            service: The Google Drive API service (authenticated with service account)
            organisation_id: The ID of the organisation
            folder_id: The ID of the folder to fetch contents from

        Returns:
            Tuple containing:
            - files_synced: Number of files synced
            - folders_synced: Number of folders synced
        """
        files_synced = 0
        folders_synced = 0

        query = f"'{folder_id}' in parents and trashed = false"
        fields = "nextPageToken, files(id, name, mimeType, parents, createdTime, modifiedTime, size, webViewLink, permissions(emailAddress, role, type))"

        page_token = None
        while True:
            try:
                response = service.files().list(
                    q=query,
                    spaces='drive',
                    fields=fields,
                    pageToken=page_token,
                    pageSize=100
                ).execute()

                items = response.get('files', [])

                for item in items:
                    is_folder = item['mimeType'] == 'application/vnd.google-apps.folder'

                    if is_folder:
                        # Create or update folder using existing permissions-based function
                        self._create_or_update_folder_with_permissions(item, organisation_id)
                        folders_synced += 1

                        # Process folder permissions to create users and user-folder relationships
                        self._process_folder_permissions(item, organisation_id)

                        # Create folder relationships immediately
                        self._create_folder_folder_relationships_immediate(item, organisation_id)

                        # Recursive call to fetch subfolders
                        sub_files, sub_folders = self._fetch_folder_contents_recursively_with_permissions(
                            service,
                            organisation_id,
                            item['id']
                        )
                        files_synced += sub_files
                        folders_synced += sub_folders
                    else:
                        # Create or update file using existing permissions-based function
                        # This function handles Neo4j and Pinecone automatically
                        self._create_or_update_file_with_permissions(item, organisation_id)
                        files_synced += 1

                        # Process file permissions to create users and user-file relationships
                        self._process_file_permissions(item, organisation_id)

                        # Create file-folder relationships immediately
                        self._create_file_folder_relationships_immediate(item, organisation_id)

                page_token = response.get('nextPageToken')
                if not page_token:
                    break

            except Exception as e:
                logger.error(f"Error processing folder contents recursively with permissions: {str(e)}")
                break

        return files_synced, folders_synced

    def _extract_user_id_from_permissions(self, file_data: Dict[str, Any], organisation_id: str) -> str:
        """
        Extract a real user ID from file permissions instead of using virtual service account user ID.
        
        Args:
            file_data: The file data from Google Drive API containing permissions
            organisation_id: The ID of the organisation
            
        Returns:
            User ID extracted from permissions, or owner email if no user permissions found
        """
        try:
            permissions = file_data.get('permissions', [])
            
            # First, try to find a user with 'owner' role
            for permission in permissions:
                if (permission.get('type') == 'user' and
                    permission.get('role') == 'owner' and
                    permission.get('emailAddress')):
                    email = permission['emailAddress']
                    # Exclude service account emails
                    if not email.endswith('.iam.gserviceaccount.com'):
                        # Try to find existing user by email
                        user_result = execute_read_query(
                            self.sync_queries.FIND_USER_BY_EMAIL,
                            {'email': email}
                        )
                        if user_result and user_result[0].get('user_id'):
                            return user_result[0]['user_id']
                        else:
                            # Create user if not exists and return the user ID
                            return self._create_or_find_user_by_email(email, organisation_id)
            
            # If no owner found, try to find any user with editor permissions
            for permission in permissions:
                if (permission.get('type') == 'user' and
                    permission.get('role') == 'editor' and
                    permission.get('emailAddress')):
                    email = permission['emailAddress']
                    # Exclude service account emails
                    if not email.endswith('.iam.gserviceaccount.com'):
                        # Try to find existing user by email
                        user_result = execute_read_query(
                            self.sync_queries.FIND_USER_BY_EMAIL,
                            {'email': email}
                        )
                        if user_result and user_result[0].get('user_id'):
                            return user_result[0]['user_id']
                        else:
                            # Create user if not exists and return the user ID
                            return self._create_or_find_user_by_email(email, organisation_id)
            
            # If no owner or editor found, try to find any user with any permissions
            for permission in permissions:
                if (permission.get('type') == 'user' and
                    permission.get('emailAddress')):
                    email = permission['emailAddress']
                    # Exclude service account emails
                    if not email.endswith('.iam.gserviceaccount.com'):
                        # Try to find existing user by email
                        user_result = execute_read_query(
                            self.sync_queries.FIND_USER_BY_EMAIL,
                            {'email': email}
                        )
                        if user_result and user_result[0].get('user_id'):
                            return user_result[0]['user_id']
                        else:
                            # Create user if not exists and return the user ID
                            return self._create_or_find_user_by_email(email, organisation_id)
            
            # If no user permissions found, fall back to service account pattern
            # This should be rare, but provides a fallback
            logger.warning(f"No user permissions found for file {file_data.get('name', 'unknown')}, using service account fallback")
            return f"sa_{organisation_id}"
            
        except Exception as e:
            logger.error(f"Error extracting user ID from permissions: {str(e)}")
            # Fallback to service account pattern on error
            return f"sa_{organisation_id}"