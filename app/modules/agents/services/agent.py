import uuid
from datetime import datetime, timezone
import structlog
import grpc

from app.db.neo4j import execute_write_query, execute_read_query
from app.grpc_ import agent_graph_pb2, agent_graph_pb2_grpc
from app.modules.agents.models.agent import Agent
from app.modules.agents.repository.agent_queries import AgentQueries
from app.modules.agents.repository.agent_relationship_queries import AgentRelationshipQueries
from app.utils.constants.departments import DefaultDepartments
logger = structlog.get_logger()


class AgentService(agent_graph_pb2_grpc.AgentGraphServiceServicer):
    """
    Service handling agent operations in Neo4j database using repository pattern.
    """

    def __init__(self):
        """Initialize the service with repository instances."""
        self.agent_queries = AgentQueries()
        self.relationship_queries = AgentRelationshipQueries()

    def createAgent(self, request, context):
        """
        Create a new agent in Neo4j.
        Establishes relationships with:
        - Owner (User) via OWNS relationship
        - Department via BELONGS_TO relationship
        - Users via HAS_ACCESS relationship
        
        Validates:
        - Department belongs to the same organization as the owner
        - All user_ids are valid users
        """
        logger.info("Received request to create agent", name=request.name)
        
        try:
            # Verify owner (user) exists
            owner_result = execute_read_query(
                self.agent_queries.VALIDATE_OWNER_EXISTS, 
                {"user_id": request.owner.id}
            )
            
            if not owner_result:
                logger.error(f"Owner user {request.owner.id} not found")
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Owner user with ID {request.owner.id} not found")
                return agent_graph_pb2.CreateAgentResponse(success=False, message=f"Owner user with ID {request.owner.id} not found")
            
            # Verify department exists
            dept_result = execute_read_query(
                self.agent_queries.VALIDATE_DEPARTMENT_EXISTS,
                {"dept_id": request.department}
            )
            
            if not dept_result:
                logger.error(f"Department {request.department} not found")
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Department with ID {request.department} not found")
                return agent_graph_pb2.CreateAgentResponse(success=False, message=f"Department with ID {request.department} not found")
            
            # Validate that the department belongs to the same organization as the owner
            org_validation_result = execute_read_query(
                self.agent_queries.VALIDATE_OWNER_DEPARTMENT_ORG,
                {"user_id": request.owner.id, "dept_id": request.department, "gen_dept": DefaultDepartments.GENERAL.value}
            )
            
            if not org_validation_result:
                logger.error(f"Owner user {request.owner.id} is not a member of any organization or department validation failed")
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details(f"Owner user with ID {request.owner.id} is not a member of any organization")
                return agent_graph_pb2.CreateAgentResponse(success=False, message=f"Owner user with ID {request.owner.id} is not a member of any organization")
            
            org_id = org_validation_result[0]['organisation_id']
            
            if not org_id:
                logger.error(f"Department {request.department} does not belong to the same organization as the owner {request.owner.id}")
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details(f"Department and owner must belong to the same organization")
                return agent_graph_pb2.CreateAgentResponse(success=False, message=f"Department and owner must belong to the same organization")
            
            # Validate that all user_ids are valid users
            if request.user_ids:
                user_validation_result = execute_read_query(
                    self.agent_queries.VALIDATE_USERS_EXIST,
                    {"user_ids": list(request.user_ids)}
                )
                
                if not user_validation_result:
                    logger.error("Failed to validate user IDs")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details("Failed to validate user IDs")
                    return agent_graph_pb2.CreateAgentResponse(success=False, message="Failed to validate user IDs")
                
                existing_users = user_validation_result[0]['existing_users']
                missing_users = set(request.user_ids) - set(existing_users)
                
                if missing_users:
                    logger.error(f"Users not found: {missing_users}")
                    context.set_code(grpc.StatusCode.NOT_FOUND)
                    context.set_details(f"Users with IDs {list(missing_users)} not found")
                    return agent_graph_pb2.CreateAgentResponse(success=False, message=f"Users with IDs {list(missing_users)} not found")
            
            # Use provided ID if it exists, otherwise generate a new UUID
            agent_id = request.id if hasattr(request, 'id') and request.id else str(uuid.uuid4())
            current_time = datetime.now(timezone.utc).isoformat()
            
            # Create the agent node in Neo4j
            params = {
                "id": agent_id,
                "name": request.name,
                "description": request.description,
                "department_id": request.department,
                "organisation_id": org_id,  # Use validated organization ID
                "owner_id": request.owner.id,
                "user_ids": list(request.user_ids),
                "created_at": current_time,
                "updated_at": current_time,
                "visibility": "PRIVATE",  # Default to PRIVATE if not specified
                "status": "ACTIVE",       # Default to ACTIVE if not specified
                "creator_role": "MEMBER"  # Default to MEMBER if not specified
            }
            
            # Override defaults if values are provided in the request with validation
            if hasattr(request, 'visibility') and request.visibility is not None:
                # Validate visibility enum
                if request.visibility in agent_graph_pb2.Visibility.values():
                    visibility_name = agent_graph_pb2.Visibility.Name(request.visibility)
                    params["visibility"] = visibility_name
                else:
                    logger.error(f"Invalid visibility value: {request.visibility}")
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(f"Invalid visibility value. Must be one of: {list(agent_graph_pb2.Visibility.keys())}")
                    return agent_graph_pb2.CreateAgentResponse(success=False, message=f"Invalid visibility value. Must be one of: {list(agent_graph_pb2.Visibility.keys())}")
            
            if hasattr(request, 'status') and request.status is not None:
                # Validate status enum
                if request.status in agent_graph_pb2.Status.values():
                    status_name = agent_graph_pb2.Status.Name(request.status)
                    params["status"] = status_name
                else:
                    logger.error(f"Invalid status value: {request.status}")
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(f"Invalid status value. Must be one of: {list(agent_graph_pb2.Status.keys())}")
                    return agent_graph_pb2.CreateAgentResponse(success=False, message=f"Invalid status value. Must be one of: {list(agent_graph_pb2.Status.keys())}")
                
            if hasattr(request, 'creator_role') and request.creator_role is not None:
                # Validate creator_role enum
                if request.creator_role in agent_graph_pb2.CreatorRole.values():
                    creator_role_name = agent_graph_pb2.CreatorRole.Name(request.creator_role)
                    params["creator_role"] = creator_role_name
                else:
                    logger.error(f"Invalid creator_role value: {request.creator_role}")
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(f"Invalid creator_role value. Must be one of: {list(agent_graph_pb2.CreatorRole.keys())}")
                    return agent_graph_pb2.CreateAgentResponse(success=False, message=f"Invalid creator_role value. Must be one of: {list(agent_graph_pb2.CreatorRole.keys())}")
            
            result = execute_write_query(self.agent_queries.CREATE_AGENT, params)
            
            if not result:
                logger.error("Failed to create agent in Neo4j")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to create agent")
                return agent_graph_pb2.CreateAgentResponse(success=False, message="Failed to create agent")
            
            # Create relationship between agent and owner
            try:
                execute_write_query(
                    self.relationship_queries.CREATE_OWNERSHIP_RELATIONSHIP,
                    {
                        "user_id": request.owner.id,
                        "agent_id": agent_id,
                        "created_at": current_time
                    }
                )
                logger.info(f"Created ownership relationship between user {request.owner.id} and agent {agent_id}")
            except Exception as e:
                logger.error("Failed to create owner-agent relationship", error=str(e))
                # We continue even if relationship creation fails
            
            # Create relationship between agent and department
            try:
                execute_write_query(
                    self.relationship_queries.CREATE_DEPARTMENT_RELATIONSHIP,
                    {
                        "dept_id": request.department,
                        "agent_id": agent_id,
                        "assigned_at": current_time
                    }
                )
                logger.info(f"Created relationship between agent {agent_id} and department {request.department}")
            except Exception as e:
                logger.error("Failed to create agent-department relationship", error=str(e))
                # We continue even if relationship creation fails
            
            # Create relationships between agent and users with access
            if request.user_ids:
                try:
                    execute_write_query(
                        self.relationship_queries.CREATE_BATCH_ACCESS_RELATIONSHIPS,
                        {
                            "agent_id": agent_id,
                            "user_ids": list(request.user_ids),
                            "access_type": "read",
                            "granted_at": current_time,
                            "granted_by": request.owner.id
                        }
                    )
                    logger.info(f"Created access relationships for agent {agent_id}")
                except Exception as e:
                    logger.error(f"Failed to create user-agent access relationships", error=str(e))
                    # We continue even if relationship creation fails
            
            return agent_graph_pb2.CreateAgentResponse(
                success=True,
                message="Agent created successfully"
            )
            
        except Exception as e:
            logger.error("Error creating agent", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error creating agent: {str(e)}")
            return agent_graph_pb2.CreateAgentResponse(success=False, message=f"Error creating agent: {str(e)}")

    def getAgent(self, request, context):
        """
        Get agent details by ID.
        Includes information about:
        - The agent itself
        - The department it belongs to
        - The owner
        - Users who have access to it
        """
        logger.info("Received request to get agent", id=request.id)
        
        try:
            # Query for the agent and its relationships using repository
            result = execute_read_query(
                self.agent_queries.GET_AGENT_WITH_RELATIONSHIPS,
                {"id": request.id}
            )
            
            if not result:
                logger.error("Agent not found", id=request.id)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent with ID {request.id} not found")
                return agent_graph_pb2.AgentResponse(success=False, message=f"Agent with ID {request.id} not found")
            
            agent_data = result[0]['a']
            department_data = result[0]['d']
            owner_data = result[0]['u']
            access_users_data = result[0]['access_users']
            
            # Extract user IDs from access users
            access_user_ids = [user.get('id') for user in access_users_data if user]
            
            # Convert Neo4j format to proto format
            agent_model = agent_graph_pb2.Agent(
                id=agent_data.get('id', ''),
                name=agent_data.get('name', ''),
                description=agent_data.get('description', ''),
                owner_id=owner_data.get('id', '') if owner_data else '',
                owner_name=owner_data.get('name', '') if owner_data else '',
                user_ids=access_user_ids,
                department=department_data.get('id', '') if department_data else '',
                created_at=agent_data.get('created_at', ''),
                updated_at=agent_data.get('updated_at', ''),
                visibility=agent_graph_pb2.Visibility.PRIVATE if agent_data.get('visibility', 'PRIVATE') == 'PRIVATE' else agent_graph_pb2.Visibility.PUBLIC,
                status=agent_graph_pb2.Status.ACTIVE if agent_data.get('status', 'ACTIVE') == 'ACTIVE' else
                       agent_graph_pb2.Status.BENCH if agent_data.get('status', '') == 'BENCH' else agent_graph_pb2.Status.INACTIVE,
                creator_role=agent_graph_pb2.CreatorRole.MEMBER if agent_data.get('creator_role', 'MEMBER') == 'MEMBER' else
                            agent_graph_pb2.CreatorRole.CREATOR if agent_data.get('creator_role', '') == 'CREATOR' else agent_graph_pb2.CreatorRole.VIEWER
            )
            
            return agent_graph_pb2.AgentResponse(
                success=True,
                message="Agent retrieved successfully",
                agent=agent_model
            )
            
        except Exception as e:
            logger.error("Error getting agent", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error retrieving agent: {str(e)}")
            return agent_graph_pb2.AgentResponse(success=False, message=f"Error retrieving agent: {str(e)}")

    def listAgentsByUserId(self, request, context):
        """
        Get all agents that a user has access to.
        """
        logger.info("Received request to list agents for user", owner_id=request.owner_id)
        
        try:
            # Query for agents that the user has access to using repository
            results = execute_read_query(
                self.agent_queries.LIST_AGENTS_BY_USER,
                {"user_id": request.owner_id}
            )
            
            if not results:
                # No agents found, but this is not an error
                return agent_graph_pb2.ListAgentsResponse(
                    success=True,
                    agents=[],
                    total=0,
                    page=request.page,
                    total_pages=0
                )
            
            agents = []
            for result in results:
                agent_data = result['a']
                owner_data = result['owner']
                
                agent_model = agent_graph_pb2.Agent(
                    id=agent_data.get('id', ''),
                    name=agent_data.get('name', ''),
                    description=agent_data.get('description', ''),
                    owner_id=owner_data.get('id', '') if owner_data else '',
                    owner_name=owner_data.get('name', '') if owner_data else '',
                    user_ids=list(agent_data.get('user_ids', [])),
                    department=agent_data.get('department_id', ''),
                    created_at=agent_data.get('created_at', ''),
                    updated_at=agent_data.get('updated_at', ''),
                    visibility=agent_graph_pb2.Visibility.PRIVATE if agent_data.get('visibility', 'PRIVATE') == 'PRIVATE' else agent_graph_pb2.Visibility.PUBLIC,
                    status=agent_graph_pb2.Status.ACTIVE if agent_data.get('status', 'ACTIVE') == 'ACTIVE' else
                           agent_graph_pb2.Status.BENCH if agent_data.get('status', '') == 'BENCH' else agent_graph_pb2.Status.INACTIVE,
                    creator_role=agent_graph_pb2.CreatorRole.MEMBER if agent_data.get('creator_role', 'MEMBER') == 'MEMBER' else
                                agent_graph_pb2.CreatorRole.CREATOR if agent_data.get('creator_role', '') == 'CREATOR' else agent_graph_pb2.CreatorRole.VIEWER
                )
                
                agents.append(agent_model)
            
            # Simple pagination
            page = request.page if request.page > 0 else 1
            page_size = request.page_size if request.page_size > 0 else 10
            
            total = len(agents)
            total_pages = (total + page_size - 1) // page_size
            
            start_idx = (page - 1) * page_size
            end_idx = min(start_idx + page_size, total)
            
            paginated_agents = agents[start_idx:end_idx]
            
            return agent_graph_pb2.ListAgentsResponse(
                success=True,
                agents=paginated_agents,
                total=total,
                page=page,
                total_pages=total_pages
            )
            
        except Exception as e:
            logger.error("Error listing agents for user", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error listing agents: {str(e)}")
            return agent_graph_pb2.ListAgentsResponse(success=False)

    def getAllAgentsFromOrganisation(self, request, context):
        """
        Get all agents from all departments of an organization.
        """
        logger.info("Received request to get all agents from organization", org_id=request.organisation_id)
        
        try:
            # Query for all agents in the organization through departments using repository
            results = execute_read_query(
                self.agent_queries.LIST_AGENTS_BY_ORGANIZATION,
                {"org_id": request.organisation_id}
            )
            
            if not results:
                # No agents found, but this is not an error
                return agent_graph_pb2.ListAgentsResponse(
                    success=True,
                    agents=[],
                    total=0,
                    page=1,
                    total_pages=0
                )
            
            agents = []
            for result in results:
                agent_data = result['a']
                owner_data = result['owner']
                
                agent_model = agent_graph_pb2.Agent(
                    id=agent_data.get('id', ''),
                    name=agent_data.get('name', ''),
                    description=agent_data.get('description', ''),
                    owner_id=owner_data.get('id', '') if owner_data else '',
                    owner_name=owner_data.get('name', '') if owner_data else '',
                    user_ids=list(agent_data.get('user_ids', [])),
                    department=agent_data.get('department_id', ''),
                    created_at=agent_data.get('created_at', ''),
                    updated_at=agent_data.get('updated_at', ''),
                    visibility=agent_graph_pb2.Visibility.PRIVATE if agent_data.get('visibility', 'PRIVATE') == 'PRIVATE' else agent_graph_pb2.Visibility.PUBLIC,
                    status=agent_graph_pb2.Status.ACTIVE if agent_data.get('status', 'ACTIVE') == 'ACTIVE' else
                           agent_graph_pb2.Status.BENCH if agent_data.get('status', '') == 'BENCH' else agent_graph_pb2.Status.INACTIVE,
                    creator_role=agent_graph_pb2.CreatorRole.MEMBER if agent_data.get('creator_role', 'MEMBER') == 'MEMBER' else
                                agent_graph_pb2.CreatorRole.CREATOR if agent_data.get('creator_role', '') == 'CREATOR' else agent_graph_pb2.CreatorRole.VIEWER
                )
                
                agents.append(agent_model)
            
            return agent_graph_pb2.ListAgentsResponse(
                success=True,
                agents=agents,
                total=len(agents),
                page=1,
                total_pages=1
            )
            
        except Exception as e:
            logger.error("Error getting agents from organization", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error retrieving agents: {str(e)}")
            return agent_graph_pb2.ListAgentsResponse(success=False)

    def getAgentsFromDepartment(self, request, context):
        """
        Get all agents from a specific department.
        """
        logger.info("Received request to get agents from department", dept_id=request.department_id)
        
        try:
            # Query for all agents in the department using repository
            results = execute_read_query(
                self.agent_queries.LIST_AGENTS_BY_DEPARTMENT,
                {"dept_id": request.department_id}
            )
            
            if not results:
                # No agents found, but this is not an error
                return agent_graph_pb2.ListAgentsResponse(
                    success=True,
                    agents=[],
                    total=0,
                    page=1,
                    total_pages=0
                )
            
            agents = []
            for result in results:
                agent_data = result['a']
                owner_data = result['owner']
                
                agent_model = agent_graph_pb2.Agent(
                    id=agent_data.get('id', ''),
                    name=agent_data.get('name', ''),
                    description=agent_data.get('description', ''),
                    owner_id=owner_data.get('id', '') if owner_data else '',
                    # owner_name=owner_data.get('name', '') if owner_data else '',
                    owner_name=owner_data.get('name') or owner_data.get('email', '') if owner_data else '',
                    user_ids=list(agent_data.get('user_ids', [])),
                    department=request.department_id,
                    created_at=agent_data.get('created_at', ''),
                    updated_at=agent_data.get('updated_at', ''),
                    visibility=agent_graph_pb2.Visibility.PRIVATE if agent_data.get('visibility', 'PRIVATE') == 'PRIVATE' else agent_graph_pb2.Visibility.PUBLIC,
                    status=agent_graph_pb2.Status.ACTIVE if agent_data.get('status', 'ACTIVE') == 'ACTIVE' else
                           agent_graph_pb2.Status.BENCH if agent_data.get('status', '') == 'BENCH' else agent_graph_pb2.Status.INACTIVE,
                    creator_role=agent_graph_pb2.CreatorRole.MEMBER if agent_data.get('creator_role', 'MEMBER') == 'MEMBER' else
                                agent_graph_pb2.CreatorRole.CREATOR if agent_data.get('creator_role', '') == 'CREATOR' else agent_graph_pb2.CreatorRole.VIEWER
                )
                print("Agent model : ",agent_model)
                agents.append(agent_model)
            
            return agent_graph_pb2.ListAgentsResponse(
                success=True,
                agents=agents,
                total=len(agents),
                page=1,
                total_pages=1
            )
            
        except Exception as e:
            logger.error("Error getting agents from department", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error retrieving agents: {str(e)}")
            return agent_graph_pb2.ListAgentsResponse(success=False)

    def checkUserAgentAccess(self, request, context):
        """
        Check if a user has access to a specific agent.
        Returns true if:
        - User is the owner of the agent
        - User has been granted access to the agent
        """
        logger.info("Received request to check user access to agent",
                   user_id=request.user_id, agent_id=request.agent_id)
        
        try:
            # Use the unified access control function
            from app.utils.access_control import check_user_access
            
            user_has_access = check_user_access(request.user_id, request.agent_id, 'agent')
            
            return agent_graph_pb2.CheckAccessResponse(
                success=True,
                has_access=user_has_access
            )
            
        except Exception as e:
            logger.error("Error checking user access", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error checking user access: {str(e)}")
            return agent_graph_pb2.CheckAccessResponse(success=False, has_access=False)