import uuid
from datetime import datetime
import structlog
import grpc

from app.db.neo4j import execute_write_query, execute_read_query
from app.modules.organisation.repository.department import DepartmentQueries
from app.grpc_ import organisation_pb2, organisation_pb2_grpc
from app.utils.constants.departments import (
    DefaultRoles,
    DefaultDepartments,
    DefaultPermissions,
    Visibility
)

logger = structlog.get_logger()
      

class DepartmentService(organisation_pb2_grpc.OrganisationServiceServicer):
    def __init__(self):
        self.queries = DepartmentQueries()

    def createDepartment(self, request, context):
        """
        Create a new department within an organisation.
        Only admins can create departments, and department names must be unique within an organisation.
        """
        logger.info("Received request to create department", 
                   name=request.name, org_id=request.organisation_id)
        
        try:
            # Single query to validate all prerequisites
            validation_result = execute_read_query(self.queries.VALIDATE_DEPARTMENT_CREATION, {
                "org_id": request.organisation_id,
                "user_id": request.created_by,
                "general_dept": DefaultDepartments.GENERAL.value,
                "admin_role": DefaultRoles.ADMIN.value,
                "dept_name": request.name,
                "parent_id": request.parent_department_id if request.parent_department_id else None
            })
            
            if not validation_result:
                logger.error("Validation query returned no results")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Internal error during validation")
                return organisation_pb2.DepartmentResponse()
            
            result = validation_result[0]

            # Check all validation conditions
            if not result.get('org_exists'):
                logger.error("Parent organisation not found", id=request.organisation_id)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Organisation with ID {request.organisation_id} not found")
                return organisation_pb2.DepartmentResponse()
            
            if result.get('user_role') != DefaultRoles.ADMIN.value:
                logger.warning("User is not an admin of this organisation", 
                              user_id=request.created_by, org_id=request.organisation_id)
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Only administrators can create departments")
                return organisation_pb2.DepartmentResponse()
            
            if result.get('dept_exists'):
                logger.warning("Department with this name already exists", 
                              name=request.name, org_id=request.organisation_id)
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details(f"A department named '{request.name}' already exists in this organisation")
                return organisation_pb2.DepartmentResponse()
            
            if not result.get('parent_valid'):
                logger.error("Parent department not found", parent_id=request.parent_department_id)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Parent department with ID {request.parent_department_id} not found")
                return organisation_pb2.DepartmentResponse()
            
            # Generate department data
            dept_id = str(uuid.uuid4())
            current_time = datetime.utcnow().isoformat()
            visibility = self._determine_visibility(request)
            
            # Single transaction to create department with all relationships
            create_result = execute_write_query(self.queries.CREATE_DEPARTMENT, {
                "id": dept_id,
                "org_id": request.organisation_id,
                "name": request.name.upper(),
                "description": request.description,
                "parent_id": request.parent_department_id if request.parent_department_id else None,
                "created_by": request.created_by,
                "created_at": current_time,
                "updated_at": current_time,
                "visibility": visibility,
                "joined_at": current_time,
                "user_role": DefaultRoles.ADMIN.value,
                "user_permission": DefaultPermissions.UNIVERSAL.value,
            })
            
            if not create_result:
                logger.error("Failed to create department in Neo4j")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to create department")
                return organisation_pb2.DepartmentResponse()
            
            logger.info("Department created successfully", dept_id=dept_id, name=request.name)
            
            # Construct and return response
            return self._build_department_response(dept_id, request, current_time)
            
        except Exception as e:
            logger.error("Error creating department", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error creating department: {str(e)}")
            return organisation_pb2.DepartmentResponse()
    
    def _determine_visibility(self, request):
        """Determine department visibility based on request and defaults."""
        # If visibility is explicitly provided, use it
        if hasattr(request, 'visibility') and request.visibility:
            return str(request.visibility).upper()
        
        # Default: General department is PUBLIC, others are PRIVATE
        return Visibility.PUBLIC.value
    
    def _build_department_response(self, dept_id, request, current_time):
        """Build the department response object."""
        # For a newly created department, member count is 1 (the creator)
        member_count = 1
        
        # Determine visibility value
        visibility = self._determine_visibility(request)
        visibility_enum = Visibility.PRIVATE.value if visibility.upper() == "PRIVATE" else Visibility.PUBLIC.value
        
        dept_model = organisation_pb2.DepartmentModel(
            id=dept_id,
            organisation_id=request.organisation_id,
            name=request.name,
            description=request.description,
            parent_department_id=request.parent_department_id,
            created_by=request.created_by,
            created_at=current_time,
            updated_at=current_time,
            member_count=member_count,
            agent_count=0,  # Initially set to 0 as per requirements
            visibility=visibility_enum
        )
        
        return organisation_pb2.DepartmentResponse(
            department=dept_model,
            message="Department created successfully"
        )
    
    def getDepartmentUsers(self, request, context):
        """
        Get users belonging to a specific department within an organization, with pagination.
        Also fetches all departments each user is a part of in an optimized way.
        
        Args:
            organisation_id: A string org_id
            department_id: A string dept_id
            page: Page number (1-based, default: 1) - only used for internal method
            page_size: Number of items per page (default: 10) - only used for internal method
            
        Returns:
            When called as gRPC method: GetDepartmentUsersResponse
            When called internally: Tuple of (users_list, total_count, page, page_size, user_departments)
                where user_departments is a dictionary mapping user_id to a list of departments
        """
        try:
            # Called directly with parameters
            org_id = request.organisation_id
            dept_id = request.department_id if request.department_id else None
            # page and page_size are already set from function parameters
            
            # Calculate skip for pagination
            skip = (request.page - 1) * request.page_size

            # First get the total count
            count_result = execute_read_query(self.queries.GET_DEPARTMENT_USERS_COUNT, {
                "org_id": org_id,
                "dept_id": dept_id,
                "default_dept_name": DefaultDepartments.GENERAL.value
            })
            total_count = count_result[0]['user_count'] if count_result else 0
            department_det = count_result[0]['d']

            # Then get the paginated users list
            users_result = execute_read_query(self.queries.GET_DEPARTMENT_USERS, {
                "org_id": org_id,
                "dept_id": dept_id,
                "default_dept_name": DefaultDepartments.GENERAL.value,
                "skip": skip,
                "limit": request.page_size
            })
            
            if not users_result:
                logger.warning("No users found in department for the current page",
                             org_id=org_id, dept_id=dept_id or "GENERAL")
                return organisation_pb2.GetDepartmentUsersResponse(
                    department_name=department_det.get('name', ''),
                    department_desc=department_det.get('description', ''),
                    users=[],
                    total_count=total_count,
                    page=request.page,
                    page_size=request.page_size,
                    message=f"Found 0 of {total_count} total users (page {request.page})",
                    success=True
                )

            user_ids = [user_data["user"]["id"] for user_data in users_result if user_data.get("user") and user_data["user"].get("id")]
            # Create a dictionary to store user departments
            user_departments = {user_id: [] for user_id in user_ids}
            
            # Get all departments for all users in a single query
            if user_ids:
                # First try using the repository query
                all_depts_result = execute_read_query(self.queries.GET_DEPARTMENTS_FOR_USERS, {
                    "org_id": org_id,
                    "user_ids": user_ids,
                    "dept_id": dept_id
                })

            from collections import defaultdict

            user_departments = defaultdict(list)

            for entry in all_depts_result:
                dept = entry['department']
                dept_detail = organisation_pb2.UserDepartmentDetail(
                    name=dept['name'],
                    role=dept['role'],
                    permission=dept['permission']
                )
                user_departments[entry['user_id']].append(dept_detail)

            # Step 2: Combine with user data
            combined = []

            for item in users_result:
                user = item['user']
                dept_user = organisation_pb2.DepartmentUser(
                    id=user['id'],
                    name=user['name'],
                    email=user['email'],
                    departments=user_departments.get(user['id'], [])
                )
                combined.append(dept_user)
            
            
            return organisation_pb2.GetDepartmentUsersResponse(
                department_name=department_det.get('name', ''),
                department_desc=department_det.get('description', ''),
                users=combined,
                total_count=total_count,
                page=request.page,
                page_size=request.page_size,
                message=f"Found {len(combined)} of {total_count} total users (page {request.page})",
                success=True
            )
            
        except Exception as e:
            logger.error("Error fetching department users",
                        error=str(e), org_id=org_id if 'org_id' in locals() else None,
                        dept_id=dept_id if 'dept_id' in locals() else None,
                        exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error fetching users: {str(e)}")
            return organisation_pb2.GetDepartmentUsersResponse()
        
    def listDepartments(self, request, context):
        logger.info("Received request to list departments", org_id=request.organisation_id, user_id=request.user_id)
        
        try:
            # Verify that the organisation exists
            org_query = """
            MATCH (o:Organisation {id: $org_id})
            RETURN o
            """
            
            org_params = {"org_id": request.organisation_id}
            org_result = execute_read_query(org_query, org_params)
            
            if not org_result:
                logger.error("Organisation not found", id=request.organisation_id)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Organisation with ID {request.organisation_id} not found")
                return organisation_pb2.ListDepartmentsResponse()
            
            # Set default pagination values if not provided
            page = request.page if request.page > 0 else 1
            page_size = request.page_size if request.page_size > 0 else 10
            
            # Calculate skip for pagination
            skip = (page - 1) * page_size
            
            # First check if the user is an admin of the organization (admin of GENERAL department)
            admin_check_query = """
            MATCH (o:Organisation {id: $org_id})-[:HAS_DEPARTMENT]->(d:Department {name: $general_dept})
                <-[:BELONGS_TO {role: $admin_role}]-(u:User {id: $user_id})
            RETURN count(u) > 0 AS is_admin
            """
            
            admin_check_params = {
                "org_id": request.organisation_id,
                "user_id": request.user_id,
                "general_dept": DefaultDepartments.GENERAL.value,
                "admin_role": DefaultRoles.ADMIN.value
            }
            
            admin_result = execute_read_query(admin_check_query, admin_check_params)
            is_admin = admin_result[0]['is_admin'] if admin_result else False
            
            # Base query to get departments - filtered based on user's role and department visibility
            if is_admin:
                # Admin can see all departments (both PUBLIC and PRIVATE)
                query_base = """
                MATCH (o:Organisation {id: $org_id})-[:HAS_DEPARTMENT]->(d:Department)
                """
            else:
                # Non-admin users can see all PUBLIC departments and PRIVATE departments they belong to
                query_base = """
                MATCH (o:Organisation {id: $org_id})-[:HAS_DEPARTMENT]->(d:Department)
                MATCH (u:User {id: $user_id})
                WHERE d.visibility = 'PUBLIC' OR
                      EXISTS((u)-[:BELONGS_TO]->(d))
                """
            
            # Search filter - only include if search term or department ID is provided
            search_filters = []
            
            if request.search_term:
                search_filters.append("""(toLower(d.name) CONTAINS toLower($search_term)
                OR toLower(d.description) CONTAINS toLower($search_term))""")
                
            if hasattr(request, 'department_id') and request.department_id:
                search_filters.append("d.id = $department_id")
            
            # Combine search filters if any exist
            search_filter = ""
            if search_filters:
                if is_admin:
                    search_filter = "WITH o, d WHERE " + " AND ".join(search_filters)
                else:
                    search_filter = "WITH o, d, u WHERE " + " AND ".join(search_filters)

            aggregation_clause = """
            WITH d
            OPTIONAL MATCH (d)<-[:BELONGS_TO]-(anyUser:User)
            OPTIONAL MATCH (d)<-[:BELONGS_TO]-(anyAgent:Agent)
            WITH d, 
                COUNT(DISTINCT anyUser) as member_count,
                COUNT(DISTINCT anyAgent) as agent_count
            """

            count_query = query_base + search_filter + "RETURN COUNT(DISTINCT d) as total"
            
            # Construct main paginated query
            main_query = (
                query_base +
                search_filter +
                aggregation_clause +
                """
                RETURN d, member_count, agent_count
                ORDER BY d.name
                SKIP $skip
                LIMIT $limit
                """
            )
            
            # Set up parameters
            params = {
                "org_id": request.organisation_id,
                "user_id": request.user_id,
                "skip": skip,
                "limit": page_size
            }
            
            # Add search term if provided
            if request.search_term:
                params["search_term"] = request.search_term
                
            # Add department_id if provided
            if hasattr(request, 'department_id') and request.department_id:
                params["department_id"] = request.department_id
            
            # Execute count query first
            count_result = execute_read_query(count_query, params)
            total_count = count_result[0]['total'] if count_result else 0

            # Execute main query
            result = execute_read_query(main_query, params)
            
            # Convert results to department models
            departments = []
            for record in result:
                dept_data = record['d']
                member_count = record['member_count']
                agent_count = record['agent_count']
                
                # Convert visibility string to enum value
                visibility_str = dept_data.get('visibility', 'PRIVATE')
                visibility_enum = Visibility.PUBLIC.value if visibility_str.upper() == 'PUBLIC' else Visibility.PRIVATE.value
                
                dept_model = organisation_pb2.DepartmentModel(
                    id=dept_data.get('id', ''),
                    organisation_id=dept_data.get('organisation_id', ''),
                    name=dept_data.get('name', ''),
                    description=dept_data.get('description', ''),
                    parent_department_id=dept_data.get('parent_department_id', ''),
                    member_count=member_count,
                    agent_count=agent_count,
                    visibility=visibility_enum,
                    created_by=dept_data.get('created_by', ''),
                    created_at=dept_data.get('created_at', ''),
                    updated_at=dept_data.get('updated_at', '')
                )
                
                departments.append(dept_model)
            
            # Construct response
            return organisation_pb2.ListDepartmentsResponse(
                departments=departments,
                total_count=total_count,
                page=page,
                page_size=page_size
            )
            
        except Exception as e:
            logger.error("Error listing departments", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error listing departments: {str(e)}")
            return organisation_pb2.ListDepartmentsResponse()

    def grantDepartmentAccess(self, request, context):
        """
        Grant a department access to specific files and folders.

        This creates HAS_ACCESS relationships from the department to the specified files/folders.
        Validates:
        - Department exists
        - User has permission to grant access
        - Files/folders exist

        Args:
            request: Contains department_id, file_ids, folder_ids, and user_id (who is granting access)
            context: gRPC context

        Returns:
            Response indicating success/failure and message
        """
        from app.modules.organisation.utils.common import CacheInvalidationService, PermissionValidator, ResponseBuilder
        from datetime import datetime
        
        logger.info("Received request to grant department access to files/folders",
                   department_id=request.department_id, user_id=request.user_id)

        try:
            # Validate department and user permissions using shared utility
            is_valid, error_msg = PermissionValidator.validate_department_access_permission(
                request.user_id, request.department_id, self.queries, execute_read_query
            )
            
            if not is_valid:
                logger.error(error_msg)
                context.set_code(grpc.StatusCode.NOT_FOUND if "not found" in error_msg else grpc.StatusCode.PERMISSION_DENIED)
                context.set_details(error_msg)
                return ResponseBuilder.build_error_response(organisation_pb2.GrantDepartmentAccessResponse, error_msg)

            current_time = datetime.utcnow().isoformat()
            success = True
            error_messages = []

            # Create relationships to files
            if request.file_ids:
                file_params = {
                    "dept_id": request.department_id,
                    "file_ids": list(request.file_ids),
                    "current_time": current_time,
                    "user_id": request.user_id
                }

                try:
                    file_result = execute_write_query(self.queries.GRANT_DEPARTMENT_FILE_ACCESS, file_params)
                    file_count = file_result[0]['file_count'] if file_result else 0
                    logger.info(f"Granted department {request.department_id} access to {file_count} files")
                except Exception as e:
                    logger.error(f"Error granting file access: {str(e)}")
                    success = False
                    error_messages.append(f"Error granting file access: {str(e)}")

            # Create relationships to folders
            if request.folder_ids:
                folder_params = {
                    "dept_id": request.department_id,
                    "folder_ids": list(request.folder_ids),
                    "current_time": current_time,
                    "user_id": request.user_id
                }

                try:
                    folder_result = execute_write_query(self.queries.GRANT_DEPARTMENT_FOLDER_ACCESS, folder_params)
                    folder_count = folder_result[0]['folder_count'] if folder_result else 0
                    logger.info(f"Granted department {request.department_id} access to {folder_count} folders")
                except Exception as e:
                    logger.error(f"Error granting folder access: {str(e)}")
                    success = False
                    error_messages.append(f"Error granting folder access: {str(e)}")

            # Get users in department for cache invalidation
            users_result = execute_read_query(self.queries.GET_DEPARTMENT_USERS_FOR_CACHE, {
                "dept_id": request.department_id
            })

            # Invalidate access caches using shared utility
            if users_result:
                user_ids = [record['user_id'] for record in users_result]
                CacheInvalidationService.invalidate_user_caches(user_ids)
                CacheInvalidationService.invalidate_department_cache(request.department_id)

            # Construct response message
            message = "Access granted successfully" if success else "; ".join(error_messages)
            
            return ResponseBuilder.build_success_response(
                organisation_pb2.GrantDepartmentAccessResponse, message
            ) if success else ResponseBuilder.build_error_response(
                organisation_pb2.GrantDepartmentAccessResponse, message
            )

        except Exception as e:
            logger.error("Error granting department access", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error granting department access: {str(e)}")
            return ResponseBuilder.build_error_response(
                organisation_pb2.GrantDepartmentAccessResponse,
                f"Error granting department access: {str(e)}"
            )

    def addMemberToDepartment(self, request, context):
        """
        Add a member to a department.
        
        Validates:
        - Both users must be part of the same organization
        - The user adding the member must be either:
          - An admin of the department
          - The creator of the organization
        
        Args:
            request: Contains organisation_id, department_id, user_id (admin), member_id, role, permission
            context: gRPC context
            
        Returns:
            AddMemberToDepartmentResponse indicating success/failure and message
        """
        logger.info("Received request to add member to department",
                   org_id=request.organisation_id,
                   dept_id=request.department_id,
                   admin_id=request.user_id,
                   member_id=request.member_id)
        
        try:
            # Validate all prerequisites in a single query
            validation_result = execute_read_query(self.queries.VALIDATE_ADD_MEMBER_TO_DEPARTMENT, {
                "org_id": request.organisation_id,
                "dept_id": request.department_id,
                "admin_id": request.user_id,
                "member_id": request.member_id
            })
            
            if not validation_result:
                logger.error("Validation query returned no results")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Internal error during validation")
                return organisation_pb2.AddMemberToDepartmentResponse(success=False, message="Internal error during validation")
            
            result = validation_result[0]
            
            # Check all validation conditions
            if not result.get('org_exists'):
                logger.error("Organization not found", id=request.organisation_id)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Organization with ID {request.organisation_id} not found")
                return organisation_pb2.AddMemberToDepartmentResponse(success=False, message=f"Organization with ID {request.organisation_id} not found")
            
            if not result.get('dept_exists'):
                logger.error("Department not found", id=request.department_id)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Department with ID {request.department_id} not found")
                return organisation_pb2.AddMemberToDepartmentResponse(success=False, message=f"Department with ID {request.department_id} not found")
            
            if not result.get('admin_exists'):
                logger.error("Admin user not found", id=request.user_id)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Admin user with ID {request.user_id} not found")
                return organisation_pb2.AddMemberToDepartmentResponse(success=False, message=f"Admin user with ID {request.user_id} not found")
            
            if not result.get('member_exists'):
                logger.error("Member user not found", id=request.member_id)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Member user with ID {request.member_id} not found")
                return organisation_pb2.AddMemberToDepartmentResponse(success=False, message=f"Member user with ID {request.member_id} not found")
            
            if not result.get('is_authorized'):
                logger.warning("User is not authorized to add members to this department",
                              user_id=request.user_id, dept_id=request.department_id)
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Only department admins or organization creators can add members")
                return organisation_pb2.AddMemberToDepartmentResponse(success=False, message="Only department admins or organization creators can add members")
            
            if result.get('already_member'):
                logger.warning("User is already a member of this department",
                              user_id=request.member_id, dept_id=request.department_id)
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details(f"User with ID {request.member_id} is already a member of this department")
                return organisation_pb2.AddMemberToDepartmentResponse(success=False, message=f"User with ID {request.member_id} is already a member of this department")
            
            # Add member to department
            current_time = datetime.utcnow().isoformat()
            
            add_result = execute_write_query(self.queries.ADD_MEMBER_TO_DEPARTMENT, {
                "dept_id": request.department_id,
                "member_id": request.member_id,
                "role": request.role.upper() if request.role else DefaultRoles.MEMBER.value,
                "permission": request.permission.upper() if request.permission else DefaultPermissions.READ.value,
                "timestamp": current_time,
                "admin_id": request.user_id
            })
            
            if not add_result:
                logger.error("Failed to add member to department")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to add member to department")
                return organisation_pb2.AddMemberToDepartmentResponse(success=False, message="Failed to add member to department")
            
            logger.info("Member added to department successfully",
                       member_id=request.member_id, dept_id=request.department_id)
            
            return organisation_pb2.AddMemberToDepartmentResponse(
                success=True,
                message=f"Member with ID {request.member_id} added to department successfully"
            )
            
        except Exception as e:
            logger.error("Error adding member to department", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error adding member to department: {str(e)}")
            return organisation_pb2.AddMemberToDepartmentResponse(success=False, message=f"Error adding member to department: {str(e)}")

    def batchGrantDepartmentAccess(self, request, context):
        """
        Grant multiple departments access to specific files and folders in a batch operation.

        This creates HAS_ACCESS relationships from multiple departments to their specified files/folders.
        For each department, it validates:
        - Department exists
        - User has permission to grant access
        - Files/folders exist

        Args:
            request: Contains a list of department_data (each with department_id, file_ids, folder_ids) and user_id
            context: gRPC context

        Returns:
            Response indicating success/failure, message, and any failed department IDs
        """
        from app.modules.organisation.utils.common import CacheInvalidationService, PermissionValidator, ResponseBuilder
        from datetime import datetime
        
        logger.info("Received request for batch department access",
                   department_count=len(request.department_data), user_id=request.user_id)

        try:
            current_time = datetime.utcnow().isoformat()
            overall_success = True
            error_messages = []
            failed_department_ids = []

            # Process each department
            for dept_data in request.department_data:
                department_id = dept_data.department_id
                file_ids = dept_data.file_ids
                folder_ids = dept_data.folder_ids

                # Validate department and user permissions using shared utility
                is_valid, error_msg = PermissionValidator.validate_department_access_permission(
                    request.user_id, department_id, self.queries, execute_read_query
                )
                
                if not is_valid:
                    logger.error(error_msg)
                    error_messages.append(error_msg)
                    failed_department_ids.append(department_id)
                    overall_success = False
                    continue

                department_success = True

                # Create relationships to files
                if file_ids:
                    file_params = {
                        "dept_id": department_id,
                        "file_ids": list(file_ids),
                        "current_time": current_time,
                        "user_id": request.user_id
                    }

                    try:
                        file_result = execute_write_query(self.queries.GRANT_DEPARTMENT_FILE_ACCESS, file_params)
                        file_count = file_result[0]['file_count'] if file_result else 0
                        logger.info(f"Granted department {department_id} access to {file_count} files")
                    except Exception as e:
                        logger.error(f"Error granting file access for department {department_id}: {str(e)}")
                        department_success = False
                        error_messages.append(f"Error granting file access for department {department_id}: {str(e)}")

                # Create relationships to folders
                if folder_ids:
                    folder_params = {
                        "dept_id": department_id,
                        "folder_ids": list(folder_ids),
                        "current_time": current_time,
                        "user_id": request.user_id
                    }

                    try:
                        folder_result = execute_write_query(self.queries.GRANT_DEPARTMENT_FOLDER_ACCESS, folder_params)
                        folder_count = folder_result[0]['folder_count'] if folder_result else 0
                        logger.info(f"Granted department {department_id} access to {folder_count} folders")
                    except Exception as e:
                        logger.error(f"Error granting folder access for department {department_id}: {str(e)}")
                        department_success = False
                        error_messages.append(f"Error granting folder access for department {department_id}: {str(e)}")

                # If department processing failed, add to failed list
                if not department_success:
                    failed_department_ids.append(department_id)
                    overall_success = False
                else:
                    # Get users in department for cache invalidation
                    users_result = execute_read_query(self.queries.GET_DEPARTMENT_USERS_FOR_CACHE, {
                        "dept_id": department_id
                    })

                    # Invalidate access caches using shared utility
                    if users_result:
                        user_ids = [record['user_id'] for record in users_result]
                        CacheInvalidationService.invalidate_user_caches(user_ids)
                        CacheInvalidationService.invalidate_department_cache(department_id)

            # Construct response message
            if overall_success:
                message = f"Access granted successfully for all {len(request.department_data)} departments"
            else:
                message = f"Access granted with errors: {'; '.join(error_messages)}"

            return organisation_pb2.BatchGrantDepartmentAccessResponse(
                success=overall_success,
                message=message,
                failed_department_ids=failed_department_ids
            )

        except Exception as e:
            logger.error("Error in batch granting department access", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error in batch granting department access: {str(e)}")
            return organisation_pb2.BatchGrantDepartmentAccessResponse(
                success=False,
                message=f"Error in batch granting department access: {str(e)}"
            )

    def listTopLevelFolders(self, request, context):
        """
        List all top-level folders (those without parent folders) that a user has access to.
        If department_id is specified, only return folders that both the user and department have access to.

        Args:
            request: Contains user_id and optional department_id
            context: gRPC context

        Returns:
            Response containing a list of folder IDs and names
        """
        from app.modules.organisation.utils.common import PermissionValidator, ResponseBuilder
        import json
        
        logger.info("Received request to list top-level folders for user",
                   user_id=request.user_id,
                   department_id=getattr(request, 'department_id', None))

        try:
            # Check if department filter is specified
            department_id = getattr(request, 'department_id', None)

            if department_id:
                # Validate department and user permissions using shared utility
                is_valid, error_msg = PermissionValidator.validate_department_access_permission(
                    request.user_id, department_id, self.queries, execute_read_query
                )
                
                if not is_valid:
                    logger.error(error_msg)
                    context.set_code(grpc.StatusCode.NOT_FOUND if "not found" in error_msg else grpc.StatusCode.PERMISSION_DENIED)
                    context.set_details(error_msg)
                    return ResponseBuilder.build_error_response(
                        organisation_pb2.ListTopLevelFoldersResponse, error_msg
                    )

                # Get top-level folders that both the user and department have access to
                params = {
                    "user_id": request.user_id,
                    "dept_id": department_id
                }

                user_direct_dept_direct_result = execute_read_query(
                    self.queries.LIST_TOP_LEVEL_FOLDERS_USER_DIRECT_DEPT_DIRECT, params
                )
                user_dept_dept_direct_result = execute_read_query(
                    self.queries.LIST_TOP_LEVEL_FOLDERS_USER_DEPT_DEPT_DIRECT, params
                )

                # Combine and deduplicate results
                folders = {}

                if user_direct_dept_direct_result:
                    for record in user_direct_dept_direct_result:
                        folders[record['id']] = {
                            'id': record['id'],
                            'name': record['name']
                        }

                if user_dept_dept_direct_result:
                    for record in user_dept_dept_direct_result:
                        folders[record['id']] = {
                            'id': record['id'],
                            'name': record['name']
                        }

            else:
                # No department filter specified, use original behavior
                params = {
                    "user_id": request.user_id
                }

                direct_result = execute_read_query(self.queries.LIST_TOP_LEVEL_FOLDERS_DIRECT_ACCESS, params)
                dept_result = execute_read_query(self.queries.LIST_TOP_LEVEL_FOLDERS_DEPT_ACCESS, params)

                # Combine and deduplicate results
                folders = {}

                if direct_result:
                    for record in direct_result:
                        folders[record['id']] = {
                            'id': record['id'],
                            'name': record['name']
                        }

                if dept_result:
                    for record in dept_result:
                        folders[record['id']] = {
                            'id': record['id'],
                            'name': record['name']
                        }

            # Convert to list of folder models
            folder_models = []
            for folder_id, folder_data in folders.items():
                folder_model = organisation_pb2.Folder(
                    id=folder_data['id'],
                    name=folder_data['name']
                )
                folder_models.append(folder_model)

            # Add Redis caching with 15-minute TTL (optional enhancement)
            cache_key = f"top_level_folders:{request.user_id}"
            if department_id:
                cache_key += f":{department_id}"

            try:
                from app.utils.redis.redis_service import RedisService
                redis_service = RedisService()
                redis_service.set(cache_key, json.dumps([{
                    'id': model.id,
                    'name': model.name
                } for model in folder_models]), ex=900)  # 15 minutes TTL
            except Exception as e:
                logger.warning(f"Failed to cache top-level folders: {str(e)}")

            return organisation_pb2.ListTopLevelFoldersResponse(
                success=True,
                message=f"Found {len(folder_models)} top-level folders",
                folders=folder_models
            )

        except Exception as e:
            logger.error("Error listing top-level folders", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error listing top-level folders: {str(e)}")
            return ResponseBuilder.build_error_response(
                organisation_pb2.ListTopLevelFoldersResponse,
                f"Error listing top-level folders: {str(e)}"
            )
            
    def listDepartmentFolders(self, request, context):
        """
        List folders accessible by specified departments.
        
        Args:
            request: Contains organisation_id and department_ids
            context: gRPC context
            
        Returns:
            Response containing folders grouped by department
        """
        logger.info("DepartmentService: Successfully received listDepartmentFolders request",
                   org_id=request.organisation_id,
                   dept_ids=list(request.department_ids),
                   method_implemented=True)
        from app.modules.organisation.utils.common import ResponseBuilder
        
        try:
            # Validate organisation exists
            org_query = """
            MATCH (o:Organisation {id: $org_id})
            RETURN o
            """
            
            org_params = {"org_id": request.organisation_id}
            org_result = execute_read_query(org_query, org_params)
            
            if not org_result:
                logger.error("Organisation not found", id=request.organisation_id)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Organisation with ID {request.organisation_id} not found")
                return organisation_pb2.ListDepartmentFoldersResponse(success=False, message="Organisation not found")
            
            # Get department details and their accessible folders
            dept_folders_query = """
            MATCH (d:Department)
            WHERE d.id IN $dept_ids AND d.organisation_id = $org_id
            OPTIONAL MATCH (d)-[:HAS_ACCESS]->(f:GoogleDriveFolder)
            RETURN d.id as department_id, d.name as department_name,
                   collect(CASE WHEN f IS NOT NULL THEN {id: f.id, name: f.name} ELSE NULL END) as folders
            """
            
            dept_folders_params = {
                "org_id": request.organisation_id,
                "dept_ids": list(request.department_ids)
            }
            
            dept_folders_result = execute_read_query(dept_folders_query, dept_folders_params)
            
            # Process results
            department_folders = []
            
            for record in dept_folders_result:
                # Filter out NULL values from the collected folders
                folder_list = [folder for folder in record['folders'] if folder is not None]
                
                # Convert to proto models
                folder_models = []
                for folder in folder_list:
                    folder_model = organisation_pb2.Folder(
                        id=folder['id'],
                        name=folder['name']
                    )
                    folder_models.append(folder_model)
                
                # Create department folders model
                dept_folders = organisation_pb2.DepartmentFolders(
                    department_id=record['department_id'],
                    department_name=record['department_name'],
                    folders=folder_models
                )
                
                department_folders.append(dept_folders)
            
            return organisation_pb2.ListDepartmentFoldersResponse(
                success=True,
                message=f"Found folders for {len(department_folders)} departments",
                department_folders=department_folders
            )
            
        except Exception as e:
            logger.error("Error listing department folders", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error listing department folders: {str(e)}")
            return ResponseBuilder.build_error_response(
                organisation_pb2.ListDepartmentFoldersResponse,
                f"Error listing department folders: {str(e)}"
            )