from app.modules.organisation.models.schema_loader import schema

class SourceQueries:
    """Queries for source management operations."""
    
    def __init__(self):
        # Get labels and relationships from schema
        self.org_label = schema.get_node_labels()[0]  # "Organisation"
        self.user_label = schema.get_node_labels()[1]  # "User"
        self.dept_label = schema.get_node_labels()[2]  # "Department"
        
        # Get relationship types from schema
        self.has_dept_rel = schema.get_relationship_types()[1]  # "HAS_DEPARTMENT"
        self.belongs_to_rel = schema.get_relationship_types()[2]  # "BELONGS_TO"
        
        # Custom relationships for Source (not in organisation schema)
        self.has_source_rel = "HAS_SOURCE"
        self.source_label = "Source"

    @property
    def CHECK_EXISTING_SOURCE(self):
        return f"""
        MATCH (o:{self.org_label} {{id: $org_id}})-[:{self.has_source_rel}]->(s:{self.source_label})
        WHERE s.type = $source_type
        RETURN s
        """
    
    @property
    def CHECK_EXISTING_SOURCE_BY_AUTH_TYPE(self):
        return f"""
        MATCH (o:{self.org_label} {{id: $org_id}})-[:{self.has_source_rel}]->(s:{self.source_label})
        WHERE s.type = $source_type AND s.auth_type <> $auth_type
        RETURN s
        """

    @property
    def CREATE_SOURCE(self):
        return f"""
        MATCH (o:{self.org_label} {{id: $org_id}})
        CREATE (s:{self.source_label} {{
            id: $id,
            organisation_id: $org_id,
            name: $name,
            type: $type,
            auth_type: $auth_type,
            key: $key,
            jira_url: $jira_url,
            jira_email: $jira_email,
            is_validated: $is_validated,
            validation_message: $validation_message,
            last_validated_at: $last_validated_at,
            created_at: $created_at,
            updated_at: $updated_at
        }})
        CREATE (o)-[:{self.has_source_rel}]->(s)
        RETURN s
        """

    @property
    def LIST_SOURCES(self):
        return f"""
        MATCH (o:{self.org_label} {{id: $org_id}})-[:{self.has_source_rel}]->(s:{self.source_label})
        RETURN s
        """

    @property
    def VALIDATE_SOURCE_DELETE_PERMISSION(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[r:{self.belongs_to_rel}]->(d:{self.dept_label})<-[:{self.has_dept_rel}]-(o:{self.org_label})-[:{self.has_source_rel}]->(s:{self.source_label} {{id: $source_id}})
        WHERE r.permission = 'admin'
        RETURN u
        """

    @property
    def DELETE_SOURCE(self):
        return f"""
        MATCH (s:{self.source_label} {{id: $source_id}})
        DETACH DELETE s
        """

    @property
    def UPDATE_SOURCE_CREDENTIALS(self):
        return f"""
        MATCH (s:{self.source_label} {{id: $source_id}})
        SET s.key = $key,
            s.updated_at = $updated_at
        RETURN s
        """
    
    @property
    def GET_SOURCE_BY_ID(self):
        return f"""
        MATCH (o:{self.org_label})-[:{self.has_source_rel}]->(s:{self.source_label} {{id: $source_id}})
        RETURN s, o.id as org_id
        """
    
    @property
    def UPDATE_SOURCE_VALIDATION_STATUS(self):
        return f"""
        MATCH (s:{self.source_label} {{id: $source_id}})
        SET s.is_validated = $is_validated,
            s.last_validated_at = $last_validated_at,
            s.validation_message = $validation_message
        RETURN s
        """