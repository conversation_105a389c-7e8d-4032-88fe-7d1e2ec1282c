import json
import uuid
from datetime import datetime
import structlog
from app.db.neo4j import execute_write_query, execute_read_query
from app.core.config import settings
from app.utils.constants.sources import SourceType

logger = structlog.get_logger()

def migrate_credentials_to_source():
    """
    Migrate credentials from file to Source nodes in Neo4j.
    """
    logger.info("Starting migration of credentials to Source nodes")
    
    try:
        # Read existing credentials file
        with open(settings.GOOGLE_APPLICATION_CREDENTIALS, 'r') as f:
            credentials_json = f.read()
        
        # Validate JSON format
        json.loads(credentials_json)
        
        # Get all organizations
        query = """
        MATCH (o:Organization)
        RETURN o.id as id, o.name as name
        """
        
        result = execute_read_query(query)
        
        if not result:
            logger.error("No organizations found")
            return
        
        for record in result:
            org_id = record['id']
            org_name = record['name']
            
            # Check if a Source node already exists for this organization
            existing_query = """
            MATCH (o:Organization {id: $org_id})-[:HAS_SOURCE]->(s:Source)
            WHERE s.type = 'GOOGLE_DRIVE'
            RETURN s
            """
            
            existing_params = {
                "org_id": org_id
            }
            
            existing_result = execute_read_query(existing_query, existing_params)
            
            if existing_result:
                logger.info(f"Source node already exists for organization {org_name} ({org_id})")
                continue
            
            # Create a Source node for this organization
            source_id = str(uuid.uuid4())
            current_time = datetime.utcnow().isoformat()
            
            create_query = """
            MATCH (o:Organization {id: $org_id})
            CREATE (s:Source {
                id: $id,
                name: $name,
                type: $type,
                credentials: $credentials,
                created_at: $created_at,
                updated_at: $updated_at
            })
            CREATE (o)-[:HAS_SOURCE]->(s)
            RETURN s
            """
            
            create_params = {
                "org_id": org_id,
                "id": source_id,
                "name": "Google Drive",
                "type": SourceType.GOOGLE_DRIVE.value,
                "credentials": credentials_json,
                "created_at": current_time,
                "updated_at": current_time
            }
            
            execute_write_query(create_query, create_params)
            
            logger.info(f"Created Source node for organization {org_name} ({org_id})")
        
        logger.info("Migration of credentials to Source nodes completed successfully")
        
    except Exception as e:
        logger.error(f"Error migrating credentials to Source nodes: {str(e)}")

if __name__ == "__main__":
    migrate_credentials_to_source()