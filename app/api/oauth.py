"""
OAuth API Endpoints

This module provides REST API endpoints for Google OAuth authentication flow,
including initiation, callback handling, token refresh, and revocation.
"""

import structlog
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, Query, Request, Depends
from fastapi.responses import RedirectResponse
from pydantic import BaseModel

from app.utils.google_oauth_manager import oauth_manager
from app.db.neo4j import execute_write_query, execute_read_query

logger = structlog.get_logger()

router = APIRouter(prefix="/oauth", tags=["oauth"])


class OAuthInitiateRequest(BaseModel):
    organisation_id: str
    source_name: str = "Google Drive OAuth"


class OAuthInitiateResponse(BaseModel):
    success: bool
    oauth_url: str = None
    state: str = None
    message: str = None


class TokenRefreshRequest(BaseModel):
    organisation_id: str


class TokenRefreshResponse(BaseModel):
    success: bool
    message: str
    token_expires_at: str = None


class TokenRevokeRequest(BaseModel):
    organisation_id: str


class TokenRevokeResponse(BaseModel):
    success: bool
    message: str


@router.post("/google/initiate", response_model=OAuthInitiateResponse)
async def initiate_google_oauth(request: OAuthInitiateRequest):
    """
    Initiate Google OAuth flow for an organization.
    
    This endpoint generates an OAuth authorization URL and stores the state parameter
    for CSRF protection. The user should be redirected to the returned OAuth URL.
    """
    try:
        logger.info("Initiating Google OAuth flow", organisation_id=request.organisation_id)
        
        # Check if OAuth is configured
        if not oauth_manager.is_configured():
            raise HTTPException(
                status_code=500,
                detail="OAuth not configured. Missing client credentials."
            )
        
        # Check if organization already has an OAuth source
        existing_source = await _check_existing_oauth_source(request.organisation_id)
        if existing_source:
            raise HTTPException(
                status_code=409,
                detail="Organization already has an OAuth Google Drive source"
            )
        
        # Generate OAuth URL
        success, oauth_url_or_error, state = oauth_manager.generate_oauth_url(request.organisation_id)
        
        if not success:
            raise HTTPException(status_code=400, detail=oauth_url_or_error)
        
        logger.info("OAuth URL generated successfully", 
                   organisation_id=request.organisation_id,
                   state=state[:10] + "..." if state else None)
        
        return OAuthInitiateResponse(
            success=True,
            oauth_url=oauth_url_or_error,
            state=state,
            message="OAuth URL generated successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to initiate OAuth flow", 
                    error=str(e), 
                    organisation_id=request.organisation_id)
        raise HTTPException(status_code=500, detail=f"Failed to initiate OAuth flow: {str(e)}")


@router.get("/google/callback")
async def handle_google_oauth_callback(
    code: str = Query(..., description="Authorization code from Google"),
    state: str = Query(..., description="State parameter for CSRF protection"),
    error: str = Query(None, description="Error from OAuth provider")
):
    """
    Handle Google OAuth callback.
    
    This endpoint is called by Google after the user grants or denies permission.
    It exchanges the authorization code for access and refresh tokens.
    """
    try:
        logger.info("Handling OAuth callback", state=state[:10] + "..." if state else None)
        
        # Handle OAuth errors
        if error:
            logger.warning("OAuth callback received error", error=error, state=state)
            # Redirect to frontend with error
            return RedirectResponse(
                url=f"/oauth/error?error={error}&message=OAuth authorization failed"
            )
        
        # Exchange code for tokens
        success, message, token_data = oauth_manager.handle_oauth_callback(code, state)
        
        if not success:
            logger.error("OAuth callback failed", message=message, state=state)
            return RedirectResponse(
                url=f"/oauth/error?error=callback_failed&message={message}"
            )
        
        # Create OAuth source in database
        source_created = await _create_oauth_source(token_data)
        if not source_created:
            logger.error("Failed to create OAuth source", organisation_id=token_data.get('organisation_id'))
            return RedirectResponse(
                url="/oauth/error?error=source_creation_failed&message=Failed to create OAuth source"
            )
        
        logger.info("OAuth callback successful", 
                   organisation_id=token_data.get('organisation_id'),
                   user_email=token_data.get('user_email'))
        
        # Redirect to success page
        return RedirectResponse(
            url=f"/oauth/success?organisation_id={token_data.get('organisation_id')}&user_email={token_data.get('user_email')}"
        )
        
    except Exception as e:
        logger.error("OAuth callback error", error=str(e), state=state)
        return RedirectResponse(
            url=f"/oauth/error?error=internal_error&message=Internal server error"
        )


@router.post("/google/refresh", response_model=TokenRefreshResponse)
async def refresh_google_oauth_token(request: TokenRefreshRequest):
    """
    Manually refresh Google OAuth access token.
    
    This endpoint can be used to manually refresh an expired access token
    using the stored refresh token.
    """
    try:
        logger.info("Refreshing OAuth token", organisation_id=request.organisation_id)
        
        # Refresh token
        success, message, new_token_data = oauth_manager.refresh_access_token(request.organisation_id)
        
        if not success:
            raise HTTPException(status_code=400, detail=message)
        
        # Update token in database
        updated = await _update_oauth_tokens(request.organisation_id, new_token_data)
        if not updated:
            raise HTTPException(status_code=500, detail="Failed to update tokens in database")
        
        logger.info("OAuth token refreshed successfully", organisation_id=request.organisation_id)
        
        return TokenRefreshResponse(
            success=True,
            message="Token refreshed successfully",
            token_expires_at=new_token_data.get('token_expires_at')
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Token refresh failed", error=str(e), organisation_id=request.organisation_id)
        raise HTTPException(status_code=500, detail=f"Token refresh failed: {str(e)}")


@router.post("/google/revoke", response_model=TokenRevokeResponse)
async def revoke_google_oauth_token(request: TokenRevokeRequest):
    """
    Revoke Google OAuth tokens and delete OAuth source.
    
    This endpoint revokes the OAuth tokens with Google and removes the OAuth source
    from the database.
    """
    try:
        logger.info("Revoking OAuth token", organisation_id=request.organisation_id)
        
        # Revoke token with Google
        success, message = oauth_manager.revoke_token(request.organisation_id)
        
        # Delete OAuth source from database (regardless of revocation success)
        deleted = await _delete_oauth_source(request.organisation_id)
        
        if success and deleted:
            logger.info("OAuth token revoked and source deleted", organisation_id=request.organisation_id)
            return TokenRevokeResponse(
                success=True,
                message="OAuth token revoked and source deleted successfully"
            )
        elif deleted:
            logger.warning("OAuth source deleted but token revocation failed", 
                         organisation_id=request.organisation_id,
                         revoke_message=message)
            return TokenRevokeResponse(
                success=True,
                message=f"OAuth source deleted. Token revocation status: {message}"
            )
        else:
            raise HTTPException(status_code=500, detail="Failed to delete OAuth source")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Token revocation failed", error=str(e), organisation_id=request.organisation_id)
        raise HTTPException(status_code=500, detail=f"Token revocation failed: {str(e)}")


@router.get("/google/status/{organisation_id}")
async def get_oauth_status(organisation_id: str):
    """
    Get OAuth status for an organization.
    
    Returns information about the OAuth connection status, token validity,
    and user information.
    """
    try:
        logger.info("Getting OAuth status", organisation_id=organisation_id)
        
        # Check if OAuth source exists
        source_info = await _get_oauth_source_info(organisation_id)
        if not source_info:
            return {
                "success": True,
                "has_oauth": False,
                "message": "No OAuth source found"
            }
        
        # Validate token
        is_valid, validation_message = oauth_manager.validate_token(organisation_id)
        
        return {
            "success": True,
            "has_oauth": True,
            "token_valid": is_valid,
            "user_email": source_info.get('oauth_user_email'),
            "source_name": source_info.get('name'),
            "created_at": source_info.get('created_at'),
            "validation_message": validation_message
        }
        
    except Exception as e:
        logger.error("Failed to get OAuth status", error=str(e), organisation_id=organisation_id)
        raise HTTPException(status_code=500, detail=f"Failed to get OAuth status: {str(e)}")


# Helper functions

async def _check_existing_oauth_source(organisation_id: str) -> bool:
    """Check if organization already has an OAuth source."""
    try:
        query = """
        MATCH (o:Organisation {id: $org_id})-[:HAS_SOURCE]->(s:Source)
        WHERE s.type = 'google_drive' AND s.auth_type = 'oauth'
        RETURN COUNT(s) > 0 as has_oauth
        """
        
        result = execute_read_query(query, {"org_id": organisation_id})
        return result[0]['has_oauth'] if result else False
        
    except Exception as e:
        logger.error("Failed to check existing OAuth source", error=str(e))
        return False


async def _create_oauth_source(token_data: Dict[str, Any]) -> bool:
    """Create OAuth source in database."""
    try:
        import uuid
        
        source_id = str(uuid.uuid4())
        current_time = datetime.utcnow().isoformat()
        
        # Prepare OAuth credentials JSON
        oauth_credentials = {
            'access_token': token_data.get('access_token'),
            'refresh_token': token_data.get('refresh_token'),
            'token_expires_at': token_data.get('token_expires_at'),
            'scopes': token_data.get('scopes', []),
            'user_email': token_data.get('user_email'),
            'user_name': token_data.get('user_name')
        }
        
        query = """
        MATCH (o:Organisation {id: $org_id})
        CREATE (o)-[:HAS_SOURCE]->(s:Source {
            id: $source_id,
            name: $name,
            type: 'google_drive',
            auth_type: 'oauth',
            oauth_credentials: $oauth_credentials,
            oauth_user_email: $user_email,
            oauth_scopes: $scopes,
            is_validated: true,
            validation_message: 'OAuth authentication successful',
            last_validated_at: $current_time,
            created_at: $current_time,
            updated_at: $current_time
        })
        """
        
        params = {
            "org_id": token_data.get('organisation_id'),
            "source_id": source_id,
            "name": f"Google Drive OAuth - {token_data.get('user_email', 'Unknown')}",
            "oauth_credentials": json.dumps(oauth_credentials),
            "user_email": token_data.get('user_email'),
            "scopes": json.dumps(token_data.get('scopes', [])),
            "current_time": current_time
        }
        
        execute_write_query(query, params)
        logger.info("OAuth source created successfully", 
                   organisation_id=token_data.get('organisation_id'),
                   source_id=source_id)
        return True
        
    except Exception as e:
        logger.error("Failed to create OAuth source", error=str(e))
        return False


async def _update_oauth_tokens(organisation_id: str, token_data: Dict[str, Any]) -> bool:
    """Update OAuth tokens in database."""
    try:
        oauth_credentials = {
            'access_token': token_data.get('access_token'),
            'refresh_token': token_data.get('refresh_token'),
            'token_expires_at': token_data.get('token_expires_at'),
            'scopes': token_data.get('scopes', []),
            'user_email': token_data.get('user_email'),
            'user_name': token_data.get('user_name')
        }
        
        query = """
        MATCH (o:Organisation {id: $org_id})-[:HAS_SOURCE]->(s:Source)
        WHERE s.type = 'google_drive' AND s.auth_type = 'oauth'
        SET s.oauth_credentials = $oauth_credentials,
            s.updated_at = $updated_at,
            s.last_validated_at = $updated_at
        """
        
        params = {
            "org_id": organisation_id,
            "oauth_credentials": json.dumps(oauth_credentials),
            "updated_at": datetime.utcnow().isoformat()
        }
        
        execute_write_query(query, params)
        return True
        
    except Exception as e:
        logger.error("Failed to update OAuth tokens", error=str(e))
        return False


async def _delete_oauth_source(organisation_id: str) -> bool:
    """Delete OAuth source from database."""
    try:
        query = """
        MATCH (o:Organisation {id: $org_id})-[:HAS_SOURCE]->(s:Source)
        WHERE s.type = 'google_drive' AND s.auth_type = 'oauth'
        DETACH DELETE s
        """
        
        execute_write_query(query, {"org_id": organisation_id})
        return True
        
    except Exception as e:
        logger.error("Failed to delete OAuth source", error=str(e))
        return False


async def _get_oauth_source_info(organisation_id: str) -> Dict[str, Any]:
    """Get OAuth source information."""
    try:
        query = """
        MATCH (o:Organisation {id: $org_id})-[:HAS_SOURCE]->(s:Source)
        WHERE s.type = 'google_drive' AND s.auth_type = 'oauth'
        RETURN s.name as name,
               s.oauth_user_email as oauth_user_email,
               s.created_at as created_at,
               s.updated_at as updated_at
        """
        
        result = execute_read_query(query, {"org_id": organisation_id})
        return result[0] if result else None
        
    except Exception as e:
        logger.error("Failed to get OAuth source info", error=str(e))
        return None